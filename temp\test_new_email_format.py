#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的邮件发送格式（正文+HTML附件）
"""

import os
from b1_macd_1 import KLineAnalyzer

def test_new_email_format():
    """
    测试新的邮件发送格式
    """
    print("🧪 测试新的邮件发送格式")
    print("=" * 60)
    
    # 创建分析器（使用昨天的数据）
    import datetime
    yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y%m%d')
    analyzer = KLineAnalyzer(data_dir="data", kline_period="30min")
    analyzer.target_date = yesterday
    analyzer.target_path = f"data/{yesterday}"
    
    print(f"📊 配置信息:")
    print(f"   数据目录: {analyzer.target_path}")
    print(f"   K线周期: {analyzer.kline_period}")
    print(f"   目标日期: {analyzer.target_date}")
    
    # 运行分析
    print(f"\n🔍 开始分析...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n📋 分析结果:")
    print(f"   信号品种总数: {result['total_abnormal']}")
    print(f"   做多信号: {result['long_count']} 个品种")
    print(f"   做空信号: {result['short_count']} 个品种")
    print(f"   MACD背离: {result['macd_divergence_count']} 个品种")
    
    if result['abnormal_klines']:
        print(f"\n📧 测试邮件内容生成...")
        
        # 测试邮件正文（不包含图片）
        print(f"   1. 生成邮件正文...")
        email_content = analyzer.build_email_content(result['abnormal_klines'])
        
        # 保存邮件正文
        with open("test_email_body.html", "w", encoding="utf-8") as f:
            f.write(email_content)
        print(f"      ✅ 邮件正文已保存: test_email_body.html")
        
        # 测试图表HTML附件
        print(f"   2. 生成图表HTML附件...")
        charts_html = analyzer.build_charts_html_attachment(result['abnormal_klines'])
        
        # 保存图表附件
        with open("test_charts_attachment.html", "w", encoding="utf-8") as f:
            f.write(charts_html)
        print(f"      ✅ 图表附件已保存: test_charts_attachment.html")
        
        # 检查文件大小
        body_size = len(email_content.encode('utf-8')) // 1024
        charts_size = len(charts_html.encode('utf-8')) // 1024
        
        print(f"\n📏 文件大小:")
        print(f"   邮件正文: {body_size} KB")
        print(f"   图表附件: {charts_size} KB")
        print(f"   总大小: {body_size + charts_size} KB")
        
        # 检查内容特征
        print(f"\n🔍 内容检查:")
        
        # 邮件正文检查
        has_table = "<table" in email_content
        has_images = "data:image/png;base64," in email_content
        has_attachment_note = "附件" in email_content
        
        print(f"   邮件正文:")
        print(f"      包含信号表格: {'✅' if has_table else '❌'}")
        print(f"      包含内联图片: {'❌' if not has_images else '⚠️ 仍有图片'}")
        print(f"      包含附件说明: {'✅' if has_attachment_note else '❌'}")
        
        # 图表附件检查
        charts_has_images = "data:image/png;base64," in charts_html
        charts_image_count = charts_html.count("data:image/png;base64,")
        charts_has_toc = "图表目录" in charts_html
        
        print(f"   图表附件:")
        print(f"      包含图片: {'✅' if charts_has_images else '❌'}")
        print(f"      图片数量: {charts_image_count}")
        print(f"      包含目录: {'✅' if charts_has_toc else '❌'}")
        
        # 显示信号详情
        print(f"\n📊 检测到的信号:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            direction = kline.get('direction', 'UNKNOWN')
            direction_text = '做多' if direction == 'LONG' else '做空' if direction == 'SHORT' else kline['change_type']
            print(f"   {i}. {kline['symbol']} - {direction_text} {kline['price_change']:+.2f}%")
            print(f"      入场时间: {kline['datetime']}")
            print(f"      MACD柱值: {kline.get('macd_histogram', 'N/A'):.4f}")
        
        # 创建对比HTML文件
        print(f"\n📝 创建对比文件...")
        create_comparison_html(email_content, charts_html, result['abnormal_klines'])
        
        return True
    else:
        print(f"\n⚠️  未检测到MACD背离信号，无法测试邮件格式")
        return False

def create_comparison_html(email_content, charts_html, abnormal_klines):
    """
    创建新旧格式对比HTML文件
    """
    comparison_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>邮件格式对比</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 20px; text-align: center; border-radius: 10px; margin-bottom: 20px; }}
        .comparison {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }}
        .format-section {{ background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .format-title {{ background: #f8f9fa; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 10px 10px 0 0; font-weight: bold; text-align: center; }}
        .new-format {{ border-left: 5px solid #28a745; }}
        .old-format {{ border-left: 5px solid #ffc107; }}
        .preview {{ border: 1px solid #ddd; border-radius: 5px; overflow: hidden; max-height: 400px; overflow-y: auto; }}
        .preview iframe {{ width: 100%; height: 350px; border: none; }}
        .stats {{ background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        .pros-cons {{ margin: 15px 0; }}
        .pros {{ color: #28a745; }}
        .cons {{ color: #dc3545; }}
        .summary {{ background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 邮件发送格式对比</h1>
            <p>新格式：邮件正文（基本信息） + HTML附件（图表汇总）</p>
        </div>
        
        <div class="comparison">
            <div class="format-section new-format">
                <div class="format-title" style="background: #d4edda; color: #155724;">
                    ✅ 新格式 - 邮件正文（推荐）
                </div>
                
                <div class="stats">
                    <strong>特点：</strong>
                    <ul>
                        <li>只包含基本信息表格</li>
                        <li>不包含内联图片</li>
                        <li>邮件体积小，加载快</li>
                        <li>兼容性好</li>
                    </ul>
                </div>
                
                <div class="preview">
                    <iframe srcdoc='{email_content.replace("'", "&apos;")}'></iframe>
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <strong>✅ 优点：</strong>
                        <ul>
                            <li>邮件体积小（约{len(email_content.encode('utf-8')) // 1024}KB）</li>
                            <li>所有邮件客户端都能正常显示</li>
                            <li>加载速度快</li>
                            <li>信息一目了然</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <strong>⚠️ 注意：</strong>
                        <ul>
                            <li>图表需要查看附件</li>
                            <li>需要额外步骤查看图表</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="format-section old-format">
                <div class="format-title" style="background: #fff3cd; color: #856404;">
                    📊 新格式 - 图表附件
                </div>
                
                <div class="stats">
                    <strong>特点：</strong>
                    <ul>
                        <li>包含所有品种的K线图表</li>
                        <li>专业的图表布局</li>
                        <li>完整的技术分析信息</li>
                        <li>可离线查看</li>
                    </ul>
                </div>
                
                <div class="preview">
                    <p style="text-align: center; padding: 20px; background: #f8f9fa; margin: 0;">
                        📁 <strong>HTML附件预览</strong><br>
                        <small>文件大小: {len(charts_html.encode('utf-8')) // 1024}KB</small><br>
                        <a href="test_charts_attachment.html" target="_blank" style="color: #007bff;">点击查看完整图表附件</a>
                    </p>
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <strong>✅ 优点：</strong>
                        <ul>
                            <li>包含完整的K线图表</li>
                            <li>专业的技术分析展示</li>
                            <li>支持峰值标记和入场点</li>
                            <li>可保存为本地文件</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <strong>⚠️ 注意：</strong>
                        <ul>
                            <li>文件较大（{len(charts_html.encode('utf-8')) // 1024}KB）</li>
                            <li>需要下载附件查看</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="summary">
            <h3>📋 总结</h3>
            <p><strong>新的邮件发送方式优势：</strong></p>
            <ol>
                <li><strong>邮件兼容性更好：</strong>所有邮件客户端都能正常显示基本信息</li>
                <li><strong>信息层次清晰：</strong>邮件正文提供概览，附件提供详细图表</li>
                <li><strong>用户体验优化：</strong>快速查看信号，需要时再查看图表</li>
                <li><strong>技术实现简单：</strong>避免了内联图片的兼容性问题</li>
            </ol>
            
            <p><strong>检测到的信号数量：</strong>{len(abnormal_klines)} 个品种</p>
            <p><strong>文件生成：</strong></p>
            <ul>
                <li>📧 <a href="test_email_body.html">邮件正文预览</a></li>
                <li>📊 <a href="test_charts_attachment.html">图表附件预览</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
"""
    
    with open("email_format_comparison.html", "w", encoding="utf-8") as f:
        f.write(comparison_html)
    
    print(f"      ✅ 对比文件已保存: email_format_comparison.html")

def main():
    """
    主函数
    """
    try:
        success = test_new_email_format()
        
        print(f"\n" + "=" * 60)
        print(f"🎉 测试完成！")
        
        if success:
            print(f"📁 生成的文件:")
            print(f"   📧 test_email_body.html - 邮件正文预览")
            print(f"   📊 test_charts_attachment.html - 图表附件预览")
            print(f"   🔍 email_format_comparison.html - 格式对比")
            
            print(f"\n💡 建议:")
            print(f"   1. 用浏览器打开 email_format_comparison.html 查看对比")
            print(f"   2. 新格式解决了邮件图片显示问题")
            print(f"   3. 用户可以快速查看信号，需要时查看详细图表")
        else:
            print(f"⚠️  未检测到信号，无法完整测试")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
