#!/usr/bin/env python3
"""
综合测试脚本：验证30分钟K线、50根历史K线、30分钟调度间隔等功能
"""

import os
import subprocess
import sys
import datetime

def test_b2_schedule_times():
    """
    测试b2.py的30分钟调度间隔
    """
    print("=" * 60)
    print("测试b2.py调度器的30分钟间隔配置")
    print("=" * 60)
    
    try:
        # 运行b2.py info命令查看调度信息
        result = subprocess.run([sys.executable, "b2.py", "info"], 
                              capture_output=True, text=True, timeout=30)
        
        output = result.stdout
        print("b2.py调度信息:")
        print(output)
        
        # 检查是否包含30分钟间隔的时间点
        expected_times = [
            "09:00:00", "09:30:00", "10:00:00", "10:30:00", "11:00:00", "11:30:00",
            "13:30:00", "14:00:00", "14:30:00", "15:00:00",
            "21:00:00", "21:30:00", "22:00:00", "22:30:00", "23:00:00"
        ]
        
        found_times = []
        for time_point in expected_times:
            if time_point in output:
                found_times.append(time_point)
        
        print(f"\n验证结果:")
        print(f"  期望时间点: {len(expected_times)} 个")
        print(f"  找到时间点: {len(found_times)} 个")
        
        if len(found_times) == len(expected_times):
            print("✓ 30分钟间隔配置正确")
            return True
        else:
            print("✗ 30分钟间隔配置不完整")
            missing = set(expected_times) - set(found_times)
            if missing:
                print(f"  缺少时间点: {missing}")
            return False
            
    except Exception as e:
        print(f"测试b2.py调度配置失败: {e}")
        return False

def test_b1_30min_config():
    """
    测试b1.py的30分钟K线配置
    """
    print("\n" + "=" * 60)
    print("测试b1.py的30分钟K线配置")
    print("=" * 60)
    
    try:
        # 运行b1.py help命令查看配置信息
        result = subprocess.run([sys.executable, "b1.py", "help"], 
                              capture_output=True, text=True, timeout=30)
        
        output = result.stdout
        print("b1.py配置信息:")
        print(output)
        
        # 检查关键配置信息
        config_checks = [
            ("30分钟周期", "K线周期: 30分钟"),
            ("50根历史K线", "K线图历史: 50根K线"),
            ("ATR+BOLL条件", "仅使用ATR和BOLL指标"),
            ("倒数第二根K线", "只分析每个品种的倒数第二根K线")
        ]
        
        passed_checks = 0
        for check_name, check_text in config_checks:
            if check_text in output:
                print(f"✓ {check_name}: 配置正确")
                passed_checks += 1
            else:
                print(f"✗ {check_name}: 配置缺失")
        
        if passed_checks == len(config_checks):
            print(f"\n✓ b1.py配置验证通过 ({passed_checks}/{len(config_checks)})")
            return True
        else:
            print(f"\n✗ b1.py配置验证失败 ({passed_checks}/{len(config_checks)})")
            return False
            
    except Exception as e:
        print(f"测试b1.py配置失败: {e}")
        return False

def test_integration():
    """
    测试b2.py和b1.py的集成功能
    """
    print("\n" + "=" * 60)
    print("测试b2.py和b1.py集成功能")
    print("=" * 60)
    
    try:
        # 运行b2.py test命令测试完整流程
        print("运行b2.py test命令...")
        result = subprocess.run([sys.executable, "b2.py", "test"], 
                              capture_output=True, text=True, timeout=180)
        
        output = result.stdout
        print("集成测试输出:")
        print(output)
        
        # 检查集成测试的关键步骤
        integration_checks = [
            ("数据获取步骤", "第1步：运行数据获取脚本"),
            ("分析步骤", "第2步：运行K线分析脚本"),
            ("全品种处理", "全部品种"),
            ("30分钟周期", "30分钟周期" if "30分钟周期" in output else "30min"),
            ("任务完成", "任务序列执行完成")
        ]
        
        passed_checks = 0
        for check_name, check_text in integration_checks:
            if check_text in output:
                print(f"✓ {check_name}: 执行正常")
                passed_checks += 1
            else:
                print(f"? {check_name}: 未明确确认")
        
        if result.returncode == 0:
            print(f"\n✓ 集成测试执行成功")
            return True
        else:
            print(f"\n✗ 集成测试执行失败 (退出码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print("集成测试超时")
        return False
    except Exception as e:
        print(f"集成测试失败: {e}")
        return False

def check_dependencies():
    """
    检查依赖包
    """
    print("=" * 60)
    print("检查依赖包")
    print("=" * 60)
    
    dependencies = [
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("matplotlib", "图表绘制"),
        ("openpyxl", "Excel文件操作"),
        ("schedule", "定时任务")
    ]
    
    missing_deps = []
    
    for dep_name, dep_desc in dependencies:
        try:
            __import__(dep_name)
            print(f"✓ {dep_name}: 已安装 ({dep_desc})")
        except ImportError:
            print(f"✗ {dep_name}: 未安装 ({dep_desc})")
            missing_deps.append(dep_name)
    
    if missing_deps:
        print(f"\n缺少依赖包: {', '.join(missing_deps)}")
        print(f"请运行: pip install {' '.join(missing_deps)}")
        return False
    else:
        print(f"\n✓ 所有依赖包已安装")
        return True

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("30分钟K线系统综合测试")
    print("=" * 60)
    print(f"测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查必要文件
    required_files = ["b1.py", "b2.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"错误: 缺少必要文件: {missing_files}")
        return
    
    # 运行测试
    tests = [
        ("依赖包检查", check_dependencies),
        ("b2.py调度配置", test_b2_schedule_times),
        ("b1.py配置验证", test_b1_30min_config),
        ("集成功能测试", test_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"测试 {test_name} 时发生错误: {e}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！30分钟K线系统配置正确")
        print("\n系统特性:")
        print("  ✓ 30分钟K线周期")
        print("  ✓ 50根历史K线图表")
        print("  ✓ 30分钟间隔调度")
        print("  ✓ 全品种监控")
        print("  ✓ ATR+BOLL技术指标")
        print("  ✓ 响应式邮件报告")
    else:
        print(f"\n⚠️  {len(results) - passed} 个测试失败，请检查配置")

if __name__ == "__main__":
    main()
