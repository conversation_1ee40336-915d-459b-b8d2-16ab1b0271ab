from tqsdk import TqApi, TqAuth
from tqsdk.ta import BOLL, ATR, EMA
import datetime
import pandas as pd
import os
import time
from send_mail import send_mail  # 导入邮件发送函数

# 读取合约列表，保存历史K线以及BOLL指标

# id: 1234 (k线序列号)
# datetime: 1501080715000000000 (K线起点时间(按北京时间)，自unix epoch(1970-01-01 00:00:00 GMT)以来的纳秒数)
# open: 51450.0 (K线起始时刻的最新价)
# high: 51450.0 (K线时间范围内的最高价)
# low: 51450.0 (K线时间范围内的最低价)
# close: 51450.0 (K线结束时刻的最新价)
# volume: 11 (K线时间范围内的成交量)
# open_oi: 27354 (K线起始时刻的持仓量)
# close_oi: 27355 (K线结束时刻的持仓量)

# 使用BOLL指标计算中轨、上轨和下轨，其中26为周期N  ，2为参数p
def boll_line(klines):
    boll = BOLL(klines, 26, 2) # klines is a TQSDK DataFrame-like object
    midline = boll["mid"].iloc[-1]
    topline = boll["top"].iloc[-1]
    bottomline = boll["bottom"].iloc[-1]
    # print("布林带最新值：中轨：%.2f，上轨为:%.2f，下轨为:%.2f" % (midline, topline, bottomline))
    return midline, topline, bottomline

def save_kline_data(api, symbol_list, period, data_length=100):
    """
    获取并保存K线数据到指定目录

    参数:
    api: TqApi实例
    symbol_list: 合约代码列表
    period: K线周期（秒），默认30分钟
    data_length: 获取的K线数量，默认100根

    返回:
    bool: 是否成功保存数据
    """
    try:
        # 设置保存路径
        base_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
        today = datetime.datetime.now().strftime('%Y%m%d')
        save_dir = os.path.join(base_dir, today)

        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        print(f"数据将保存到目录: {save_dir}")

        for rt_symbol in symbol_list:
            print(f"\n正在处理合约: {rt_symbol}...")
            current_symbol_df = pd.DataFrame()

            try:
                klines_tq = api.get_kline_serial(rt_symbol, period, data_length=data_length)

                if klines_tq is not None and len(klines_tq) > 0:
                    print(f"成功获取 {len(klines_tq)} 条K线数据。")

                    # 计算技术指标
                    # BOLL指标
                    boll = BOLL(klines_tq, 26, 2)
                    mid = boll["mid"].iloc[-1]
                    top = boll["top"].iloc[-1]
                    bottom = boll["bottom"].iloc[-1]
                    print(f"布林带最新值: 中轨={mid:.2f}, 上轨={top:.2f}, 下轨={bottom:.2f}")

                    # ATR指标
                    atr = ATR(klines_tq, 14)  # 14周期ATR
                    atr_value = atr.atr.iloc[-1]
                    atr_tr = atr.tr.iloc[-1]
                    print(f"ATR指标最新值: ATR={atr_value:.2f}, TR={atr_tr:.2f}")

                    # EMA指标
                    ema = EMA(klines_tq, 20)  # 20周期EMA
                    ema_value = ema.ema.iloc[-1]
                    print(f"EMA指标最新值: EMA20={ema_value:.2f}")

                    # 创建DataFrame
                    klines_df_for_symbol = pd.DataFrame({
                        'datetime_nano': klines_tq['datetime'],
                        'open': klines_tq['open'],
                        'high': klines_tq['high'],
                        'low': klines_tq['low'],
                        'close': klines_tq['close'],
                        'volume': klines_tq['volume']
                    })

                    # 转换时间格式
                    klines_df_for_symbol['datetime'] = klines_df_for_symbol['datetime_nano'].apply(
                        lambda x: datetime.datetime.fromtimestamp(x / 1e9).strftime('%Y-%m-%d %H:%M:%S')
                    )

                    # 添加技术指标
                    klines_df_for_symbol['boll_midline'] = boll["mid"]
                    klines_df_for_symbol['boll_topline'] = boll["top"]
                    klines_df_for_symbol['boll_bottomline'] = boll["bottom"]
                    klines_df_for_symbol['atr'] = atr.atr
                    klines_df_for_symbol['atr_tr'] = atr.tr
                    klines_df_for_symbol['ema_20'] = ema.ema

                    # 选择需要的列
                    current_symbol_df = klines_df_for_symbol[[
                        'datetime', 'open', 'high', 'low', 'close', 'volume',
                        'boll_midline', 'boll_topline', 'boll_bottomline',
                        'atr', 'atr_tr', 'ema_20'
                    ]]

                    if not current_symbol_df.empty:
                        # 创建安全的文件名
                        safe_symbol_name = rt_symbol.replace(".", "_").replace("/", "_")
                        excel_file_name = f"{safe_symbol_name}_kline_indicators.xlsx"
                        excel_path = os.path.join(save_dir, excel_file_name)

                        # 保存到Excel
                        current_symbol_df.to_excel(excel_path, index=False, sheet_name="Klines_Indicators")
                        print(f"合约 {rt_symbol} 的数据已成功保存到 {excel_path}")
                    else:
                        print(f"合约 {rt_symbol} 没有生成可保存的数据。")
                else:
                    print(f"未能获取合约 {rt_symbol} 的K线数据。")
            except Exception as e_symbol:
                print(f"处理合约 {rt_symbol} 时发生错误: {e_symbol}")
                continue

        return True

    except Exception as e:
        print(f"保存K线数据时发生错误: {e}")
        return False

def get_symbol_list(excel_file="generated_symbols_9_2025.xlsx", max_symbols=5):
    """
    从Excel文件读取合约列表

    参数:
    excel_file: Excel文件名
    max_symbols: 最大合约数量

    返回:
    list: 合约代码列表
    """
    try:
        print("正在读取合约列表文件...")
        symbols_df = pd.read_excel(excel_file)
        if not symbols_df.empty and symbols_df.shape[1] > 0:
            symbol_list = symbols_df.iloc[:, 0].dropna().astype(str).tolist()[:max_symbols]
            if symbol_list:
                print(f"将处理以下前{max_symbols}个合约: {symbol_list}")
                return symbol_list
            else:
                print("未能从Excel文件中读取到合约代码，或合约代码数量不足。")
        else:
            print("Excel文件为空或没有列。")
    except FileNotFoundError:
        print(f"错误: {excel_file} 文件未找到。")
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")

    return []

def is_long_candle(kline, atr_value, multiplier=2.0):
    """
    判断是否为长阴线或长阳线

    参数:
    kline: K线数据
    atr_value: ATR值
    multiplier: ATR倍数阈值，默认为2.0

    返回:
    tuple: (是否为长K线, 类型描述)
    """
    candle_height = abs(kline['close'] - kline['open'])

    # 如果K线实体高度大于ATR的multiplier倍，则认为是长K线
    if candle_height > atr_value * multiplier:
        if kline['close'] > kline['open']:
            return True, "长阳线"
        else:
            return True, "长阴线"
    return False, ""

def check_price_change_threshold(kline, threshold_percent=0.1):
    """
    检测单根K线涨跌幅是否超过指定阈值

    参数:
    kline: K线数据
    threshold_percent: 涨跌幅阈值（百分比），默认0.1%

    返回:
    tuple: (是否超过阈值, 涨跌幅百分比, 涨跌类型)
    """
    try:
        open_price = kline['open']
        close_price = kline['close']

        if open_price == 0:
            return False, 0.0, ""

        # 计算涨跌幅百分比
        change_percent = ((close_price - open_price) / open_price) * 100

        # 判断是否超过阈值
        if abs(change_percent) >= threshold_percent:
            if change_percent > 0:
                return True, change_percent, "上涨"
            else:
                return True, change_percent, "下跌"

        return False, change_percent, ""

    except Exception as e:
        print(f"计算涨跌幅时发生错误: {e}")
        return False, 0.0, ""

def build_long_candle_email_content(symbol, kline, candle_type, atr_value):
    """
    构建长K线邮件内容

    参数:
    symbol: 合约代码
    kline: K线数据
    candle_type: K线类型（长阳线/长阴线）
    atr_value: ATR值

    返回:
    str: HTML格式的邮件内容
    """
    # 将时间戳转换为可读格式
    kline_time = datetime.datetime.fromtimestamp(kline['datetime'] / 1e9).strftime('%Y-%m-%d %H:%M:%S')

    # 计算K线实体高度
    candle_height = abs(kline['close'] - kline['open'])
    atr_ratio = candle_height / atr_value

    # 构建HTML邮件内容
    html_content = f"""
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; }}
            .header {{ background-color: #f0f0f0; padding: 10px; text-align: center; }}
            .content {{ padding: 20px; }}
            .highlight {{ color: red; font-weight: bold; }}
            .data-table {{ border-collapse: collapse; width: 100%; }}
            .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            .data-table th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h2>长K线提醒</h2>
        </div>
        <div class="content">
            <p>检测到<span class="highlight">{candle_type}</span>信号！</p>

            <table class="data-table">
                <tr><th>项目</th><th>数值</th></tr>
                <tr><td>合约代码</td><td>{symbol}</td></tr>
                <tr><td>时间</td><td>{kline_time}</td></tr>
                <tr><td>K线类型</td><td><span class="highlight">{candle_type}</span></td></tr>
                <tr><td>开盘价</td><td>{kline['open']:.2f}</td></tr>
                <tr><td>最高价</td><td>{kline['high']:.2f}</td></tr>
                <tr><td>最低价</td><td>{kline['low']:.2f}</td></tr>
                <tr><td>收盘价</td><td>{kline['close']:.2f}</td></tr>
                <tr><td>成交量</td><td>{kline['volume']}</td></tr>
                <tr><td>K线实体高度</td><td>{candle_height:.2f}</td></tr>
                <tr><td>ATR值</td><td>{atr_value:.2f}</td></tr>
                <tr><td>实体高度/ATR比值</td><td>{atr_ratio:.2f}</td></tr>
            </table>

            <p><strong>提醒时间：</strong>{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </body>
    </html>
    """

    return html_content

def build_price_change_email_content(symbol, kline, change_percent, change_type):
    """
    构建涨跌幅提醒邮件内容

    参数:
    symbol: 合约代码
    kline: K线数据
    change_percent: 涨跌幅百分比
    change_type: 涨跌类型（上涨/下跌）

    返回:
    str: HTML格式的邮件内容
    """
    # 将时间戳转换为可读格式
    kline_time = datetime.datetime.fromtimestamp(kline['datetime'] / 1e9).strftime('%Y-%m-%d %H:%M:%S')

    # 设置颜色
    color = "green" if change_type == "上涨" else "red"

    # 构建HTML邮件内容
    html_content = f"""
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; }}
            .header {{ background-color: #f0f0f0; padding: 10px; text-align: center; }}
            .content {{ padding: 20px; }}
            .highlight {{ color: {color}; font-weight: bold; }}
            .data-table {{ border-collapse: collapse; width: 100%; }}
            .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            .data-table th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h2>涨跌幅提醒</h2>
        </div>
        <div class="content">
            <p>检测到<span class="highlight">{change_type}</span>超过阈值！</p>

            <table class="data-table">
                <tr><th>项目</th><th>数值</th></tr>
                <tr><td>合约代码</td><td>{symbol}</td></tr>
                <tr><td>时间</td><td>{kline_time}</td></tr>
                <tr><td>涨跌类型</td><td><span class="highlight">{change_type}</span></td></tr>
                <tr><td>涨跌幅</td><td><span class="highlight">{change_percent:+.2f}%</span></td></tr>
                <tr><td>开盘价</td><td>{kline['open']:.2f}</td></tr>
                <tr><td>最高价</td><td>{kline['high']:.2f}</td></tr>
                <tr><td>最低价</td><td>{kline['low']:.2f}</td></tr>
                <tr><td>收盘价</td><td>{kline['close']:.2f}</td></tr>
                <tr><td>成交量</td><td>{kline['volume']}</td></tr>
                <tr><td>价格变动</td><td>{kline['close'] - kline['open']:+.2f}</td></tr>
            </table>

            <p><strong>提醒时间：</strong>{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </body>
    </html>
    """

    return html_content

def build_multi_symbol_email_content(long_candle_list):
    """
    构建多品种长K线汇总邮件内容

    参数:
    long_candle_list: 长K线信息列表，每个元素包含 (symbol, kline, candle_type, atr_value)

    返回:
    str: HTML格式的邮件内容
    """
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 构建HTML邮件内容
    html_content = f"""
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; }}
            .header {{ background-color: #f0f0f0; padding: 10px; text-align: center; }}
            .content {{ padding: 20px; }}
            .highlight {{ color: red; font-weight: bold; }}
            .data-table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
            .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            .data-table th {{ background-color: #f2f2f2; }}
            .summary {{ background-color: #e8f4fd; padding: 10px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h2>多品种长K线提醒汇总</h2>
        </div>
        <div class="content">
            <div class="summary">
                <p><strong>检测到 {len(long_candle_list)} 个品种出现长K线形态！</strong></p>
                <p><strong>检测时间：</strong>{current_time}</p>
            </div>

            <table class="data-table">
                <tr>
                    <th>合约代码</th>
                    <th>K线类型</th>
                    <th>时间</th>
                    <th>开盘价</th>
                    <th>收盘价</th>
                    <th>实体高度</th>
                    <th>ATR值</th>
                    <th>实体/ATR比值</th>
                </tr>
    """

    for symbol, kline, candle_type, atr_value in long_candle_list:
        kline_time = datetime.datetime.fromtimestamp(kline['datetime'] / 1e9).strftime('%Y-%m-%d %H:%M:%S')
        candle_height = abs(kline['close'] - kline['open'])
        atr_ratio = candle_height / atr_value

        html_content += f"""
                <tr>
                    <td>{symbol}</td>
                    <td><span class="highlight">{candle_type}</span></td>
                    <td>{kline_time}</td>
                    <td>{kline['open']:.2f}</td>
                    <td>{kline['close']:.2f}</td>
                    <td>{candle_height:.2f}</td>
                    <td>{atr_value:.2f}</td>
                    <td>{atr_ratio:.2f}</td>
                </tr>
        """

    html_content += """
            </table>
        </div>
    </body>
    </html>
    """

    return html_content

def build_multi_price_change_email_content(price_change_list):
    """
    构建多品种涨跌幅汇总邮件内容

    参数:
    price_change_list: 涨跌幅信息列表，每个元素包含 (symbol, kline, change_percent, change_type)

    返回:
    str: HTML格式的邮件内容
    """
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 构建HTML邮件内容
    html_content = f"""
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; }}
            .header {{ background-color: #f0f0f0; padding: 10px; text-align: center; }}
            .content {{ padding: 20px; }}
            .highlight-up {{ color: green; font-weight: bold; }}
            .highlight-down {{ color: red; font-weight: bold; }}
            .data-table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
            .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            .data-table th {{ background-color: #f2f2f2; }}
            .summary {{ background-color: #e8f4fd; padding: 10px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h2>多品种涨跌幅提醒汇总</h2>
        </div>
        <div class="content">
            <div class="summary">
                <p><strong>检测到 {len(price_change_list)} 个品种涨跌幅超过阈值！</strong></p>
                <p><strong>检测时间：</strong>{current_time}</p>
            </div>

            <table class="data-table">
                <tr>
                    <th>合约代码</th>
                    <th>涨跌类型</th>
                    <th>涨跌幅</th>
                    <th>时间</th>
                    <th>开盘价</th>
                    <th>收盘价</th>
                    <th>价格变动</th>
                    <th>成交量</th>
                </tr>
    """

    for symbol, kline, change_percent, change_type in price_change_list:
        kline_time = datetime.datetime.fromtimestamp(kline['datetime'] / 1e9).strftime('%Y-%m-%d %H:%M:%S')
        price_change = kline['close'] - kline['open']
        highlight_class = "highlight-up" if change_type == "上涨" else "highlight-down"

        html_content += f"""
                <tr>
                    <td>{symbol}</td>
                    <td><span class="{highlight_class}">{change_type}</span></td>
                    <td><span class="{highlight_class}">{change_percent:+.2f}%</span></td>
                    <td>{kline_time}</td>
                    <td>{kline['open']:.2f}</td>
                    <td>{kline['close']:.2f}</td>
                    <td><span class="{highlight_class}">{price_change:+.2f}</span></td>
                    <td>{kline['volume']}</td>
                </tr>
        """

    html_content += """
            </table>
        </div>
    </body>
    </html>
    """

    return html_content

def save_updated_kline_data(api, symbol, klines, period, data_length=100):
    """
    保存单个合约的最新K线数据到指定目录（覆盖旧数据）

    参数:
    api: TqApi实例
    symbol: 合约代码
    klines: K线数据对象
    period: K线周期（秒）
    data_length: 获取的K线数量，默认100根

    返回:
    bool: 是否成功保存数据
    """
    try:
        # 设置保存路径（与save_kline_data保持一致，使用日期目录）
        base_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
        today = datetime.datetime.now().strftime('%Y%m%d')
        save_dir = os.path.join(base_dir, today)

        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        print(f"正在保存合约 {symbol} 的最新K线数据到: {save_dir}")

        try:
            # 获取最新的K线数据
            klines_tq = api.get_kline_serial(symbol, period, data_length=data_length)

            if klines_tq is not None and len(klines_tq) > 0:
                print(f"成功获取合约 {symbol} 的 {len(klines_tq)} 条K线数据。")

                # 计算技术指标
                # BOLL指标
                boll = BOLL(klines_tq, 26, 2)
                mid = boll["mid"].iloc[-1]
                top = boll["top"].iloc[-1]
                bottom = boll["bottom"].iloc[-1]

                # ATR指标
                atr = ATR(klines_tq, 14)  # 14周期ATR
                atr_value = atr.atr.iloc[-1]
                atr_tr = atr.tr.iloc[-1]

                # EMA指标
                ema = EMA(klines_tq, 20)  # 20周期EMA
                ema_value = ema.ema.iloc[-1]

                print(f"合约 {symbol} - 布林带: 中轨={mid:.2f}, 上轨={top:.2f}, 下轨={bottom:.2f}")
                print(f"合约 {symbol} - ATR: {atr_value:.2f}, EMA20: {ema_value:.2f}")

                # 创建DataFrame
                klines_df_for_symbol = pd.DataFrame({
                    'datetime_nano': klines_tq['datetime'],
                    'open': klines_tq['open'],
                    'high': klines_tq['high'],
                    'low': klines_tq['low'],
                    'close': klines_tq['close'],
                    'volume': klines_tq['volume']
                })

                # 转换时间格式
                klines_df_for_symbol['datetime'] = klines_df_for_symbol['datetime_nano'].apply(
                    lambda x: datetime.datetime.fromtimestamp(x / 1e9).strftime('%Y-%m-%d %H:%M:%S')
                )

                # 添加技术指标
                klines_df_for_symbol['boll_midline'] = boll["mid"]
                klines_df_for_symbol['boll_topline'] = boll["top"]
                klines_df_for_symbol['boll_bottomline'] = boll["bottom"]
                klines_df_for_symbol['atr'] = atr.atr
                klines_df_for_symbol['atr_tr'] = atr.tr
                klines_df_for_symbol['ema_20'] = ema.ema

                # 选择需要的列
                current_symbol_df = klines_df_for_symbol[[
                    'datetime', 'open', 'high', 'low', 'close', 'volume',
                    'boll_midline', 'boll_topline', 'boll_bottomline',
                    'atr', 'atr_tr', 'ema_20'
                ]]

                if not current_symbol_df.empty:
                    # 创建安全的文件名
                    safe_symbol_name = symbol.replace(".", "_").replace("/", "_")
                    excel_file_name = f"{safe_symbol_name}_kline_indicators_latest.xlsx"
                    excel_path = os.path.join(save_dir, excel_file_name)

                    # 保存到Excel（覆盖旧文件）
                    current_symbol_df.to_excel(excel_path, index=False, sheet_name="Klines_Indicators")
                    print(f"合约 {symbol} 的最新数据已成功保存到 {excel_path}")
                    return True
                else:
                    print(f"合约 {symbol} 没有生成可保存的数据。")
                    return False
            else:
                print(f"未能获取合约 {symbol} 的K线数据。")
                return False

        except Exception as e_symbol:
            print(f"处理合约 {symbol} 时发生错误: {e_symbol}")
            return False

    except Exception as e:
        print(f"保存合约 {symbol} K线数据时发生错误: {e}")
        return False

def save_all_updated_kline_data(api, symbol_list, period, data_length=100):
    """
    批量保存所有合约的最新K线数据到指定目录（覆盖旧数据）

    参数:
    api: TqApi实例
    symbol_list: 合约代码列表
    period: K线周期（秒）
    data_length: 获取的K线数量，默认100根

    返回:
    dict: 保存结果字典，键为合约代码，值为保存是否成功
    """
    results = {}

    try:
        # 设置保存路径
        base_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"

        if not os.path.exists(base_dir):
            os.makedirs(base_dir)

        print(f"开始批量保存 {len(symbol_list)} 个合约的最新K线数据到: {base_dir}")

        for symbol in symbol_list:
            try:
                success = save_updated_kline_data(api, symbol, None, period, data_length)
                results[symbol] = success

            except Exception as e:
                print(f"批量保存合约 {symbol} 时发生错误: {e}")
                results[symbol] = False
                continue

        # 统计保存结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(symbol_list)

        print(f"批量保存完成：成功 {success_count}/{total_count} 个合约")

        return results

    except Exception as e:
        print(f"批量保存K线数据时发生错误: {e}")
        return results

def main(api,T_period):
    """
    主函数
    """
    try:
        # 创建API实例
        # api = TqApi(auth=TqAuth("a_1325", "Az4719197"))

        # 获取合约列表
        symbol_list = get_symbol_list()

        if not symbol_list:
            print("没有合约可供处理。程序将退出。")
            return

        # 保存K线数据
        success = save_kline_data(api, symbol_list, period=T_period, data_length=100)

        if success:
            print("\n所有数据已成功保存。")
        else:
            print("\n保存数据时发生错误。")

    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
    #finally:
        #print("\n关闭API连接...")
        #api.close()
        #print("API连接已关闭。")

# 全局变量：邮件发送记录，用于控制发送频率
email_send_records = {}

def create_api_connection(max_retries=3, retry_delay=5):
    """
    创建天勤API连接，包含重试机制

    参数:
    max_retries: 最大重试次数
    retry_delay: 重试间隔（秒）

    返回:
    TqApi实例或None
    """
    for attempt in range(max_retries):
        try:
            print(f"正在连接天勤API... (尝试 {attempt + 1}/{max_retries})")
            api = TqApi(auth=TqAuth("a_1325", "Az4719197"))
            print("天勤API连接成功")
            return api
        except Exception as e:
            print(f"连接失败: {e}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print("达到最大重试次数，连接失败")
                return None

def safe_wait_update(api, timeout=30):
    """
    安全的等待更新函数，包含超时和异常处理

    参数:
    api: TqApi实例
    timeout: 超时时间（秒）

    返回:
    bool: 是否成功等待到更新
    """
    try:
        return api.wait_update(timeout=timeout)
    except Exception as e:
        print(f"等待更新时发生错误: {e}")
        return False

def check_long_candle_for_symbol(symbol, klines, multiplier=2.0, email_interval=300):
    """
    检查单个品种的长K线

    参数:
    symbol: 合约代码
    klines: K线数据
    multiplier: ATR倍数阈值
    email_interval: 邮件发送间隔（秒），默认5分钟

    返回:
    bool: 是否发送了邮件
    """
    try:
        # 获取最新的K线数据
        latest_kline = klines.iloc[-1]

        # 计算ATR值用于长K线判断
        atr = ATR(klines, 14)  # 14周期ATR
        atr_value = atr.atr.iloc[-1]

        # 判断是否为长K线
        is_long, candle_type = is_long_candle(latest_kline, atr_value, multiplier)

        if is_long:
            print(f"检测到{candle_type}！合约：{symbol}")

            # 检查邮件发送频率控制
            current_time = time.time()
            last_send_time = email_send_records.get(symbol, 0)

            if current_time - last_send_time >= email_interval:
                # 构建邮件内容
                email_content = build_long_candle_email_content(symbol, latest_kline, candle_type, atr_value)
                email_subject = f"长K线提醒 - {symbol} {candle_type}"

                # 发送邮件
                try:
                    send_mail(
                        mail_content=email_content,
                        subject=email_subject,
                        receives=['<EMAIL>']  # 可以根据需要修改接收邮箱
                    )
                    print(f"长K线提醒邮件已发送：{symbol} {candle_type}")

                    # 更新发送记录
                    email_send_records[symbol] = current_time
                    return True
                except Exception as e:
                    print(f"发送邮件时发生错误：{e}")
                    return False
            else:
                remaining_time = int(email_interval - (current_time - last_send_time))
                print(f"合约 {symbol} 检测到{candle_type}，但距离上次发送邮件不足{email_interval}秒，还需等待{remaining_time}秒")
                return False

    except Exception as e:
        print(f"检测合约 {symbol} 长K线时发生错误：{e}")
        return False

    return False

if __name__ == "__main__":
    api = None
    try:
        # 创建API连接，使用重试机制
        api = create_api_connection(max_retries=3, retry_delay=5)
        if api is None:
            print("无法连接到天勤API，程序退出")
            exit()

        T_period_1 = 60*10
        T_period_2 = 60*10

        # 配置选项
        SAVE_ON_UPDATE = True  # 是否在每次K线更新时保存数据
        DATA_LENGTH = 100      # 保存的K线数量
        MAX_SYMBOLS = 10       # 最大监控合约数量

        # 涨跌幅检测配置
        ENABLE_PRICE_CHANGE_ALERT = True  # 是否启用涨跌幅提醒
        PRICE_CHANGE_THRESHOLD = 0.1      # 涨跌幅阈值（百分比）

        # 长K线检测配置
        ENABLE_LONG_CANDLE_ALERT = True   # 是否启用长K线提醒
        LONG_CANDLE_MULTIPLIER = 2.0      # ATR倍数阈值

        # 获取合约列表
        symbol_list = get_symbol_list(max_symbols=MAX_SYMBOLS)  # 可以调整监控的合约数量

        if not symbol_list:
            print("没有合约可供处理。程序将退出。")
            exit()

        print(f"开始监控以下合约的长K线形态：{symbol_list}")

        # 为每个合约创建K线数据对象
        klines_dict = {}
        for symbol in symbol_list:
            try:
                klines_dict[symbol] = api.get_kline_serial(symbol, T_period_1)
                print(f"成功获取合约 {symbol} 的K线数据")
            except Exception as e:
                print(f"获取合约 {symbol} K线数据失败：{e}")
                continue

        if not klines_dict:
            print("没有成功获取任何合约的K线数据。程序将退出。")
            exit()

        # 执行一次主函数（保存历史数据到日期目录）
        main(api, T_period_2)

        # 保存一次最新数据到data目录（覆盖模式）
        print("正在保存初始最新K线数据...")
        save_results = save_all_updated_kline_data(api, symbol_list, T_period_1, data_length=DATA_LENGTH)
        success_symbols = [symbol for symbol, success in save_results.items() if success]
        print(f"初始数据保存完成，成功保存 {len(success_symbols)} 个合约的数据")

        print("开始监控K线更新...")

        while safe_wait_update(api, timeout=30):
            # 收集当前周期内检测到的长K线和涨跌幅
            current_long_candles = []
            current_price_changes = []

            # 检查每个合约的K线更新
            for symbol, klines in klines_dict.items():
                try:
                    if api.is_changing(klines.iloc[-1], "datetime"):    # 判定最后一根K线的时间是否有变化
                        print(f"合约 {symbol} K线更新")

                    # 保存最新的K线数据（覆盖旧数据）
                    if SAVE_ON_UPDATE:
                        try:
                            save_success = save_updated_kline_data(api, symbol, klines, T_period_1, data_length=DATA_LENGTH)
                            if save_success:
                                print(f"合约 {symbol} 最新K线数据已保存")
                            else:
                                print(f"合约 {symbol} K线数据保存失败")
                        except Exception as e:
                            print(f"保存合约 {symbol} K线数据时发生错误：{e}")
                    else:
                        print(f"合约 {symbol} K线更新（数据保存已禁用）")

                    # 获取最新K线数据
                    latest_kline = klines.iloc[-1]

                    # 检查涨跌幅是否超过阈值
                    if ENABLE_PRICE_CHANGE_ALERT:
                        try:
                            is_threshold_exceeded, change_percent, change_type = check_price_change_threshold(
                                latest_kline, PRICE_CHANGE_THRESHOLD
                            )

                            if is_threshold_exceeded:
                                # 检查邮件发送频率控制
                                current_time = time.time()
                                last_send_time = email_send_records.get(f"{symbol}_price", 0)

                                if current_time - last_send_time >= 300:  # 5分钟间隔
                                    current_price_changes.append((symbol, latest_kline, change_percent, change_type))
                                    print(f"检测到涨跌幅超过阈值！合约：{symbol}，{change_type} {change_percent:+.2f}%")
                                else:
                                    remaining_time = int(300 - (current_time - last_send_time))
                                    print(f"合约 {symbol} 检测到涨跌幅{change_percent:+.2f}%，但距离上次发送邮件不足300秒，还需等待{remaining_time}秒")

                        except Exception as e:
                            print(f"检测合约 {symbol} 涨跌幅时发生错误：{e}")

                    # 检查是否为长K线
                    if ENABLE_LONG_CANDLE_ALERT:
                        try:
                            atr = ATR(klines, 14)
                            atr_value = atr.atr.iloc[-1]
                            is_long, candle_type = is_long_candle(latest_kline, atr_value, multiplier=LONG_CANDLE_MULTIPLIER)

                            if is_long:
                                # 检查邮件发送频率控制
                                current_time = time.time()
                                last_send_time = email_send_records.get(f"{symbol}_long", 0)

                                if current_time - last_send_time >= 300:  # 5分钟间隔
                                    current_long_candles.append((symbol, latest_kline, candle_type, atr_value))
                                    print(f"检测到{candle_type}！合约：{symbol}")
                                else:
                                    remaining_time = int(300 - (current_time - last_send_time))
                                    print(f"合约 {symbol} 检测到{candle_type}，但距离上次发送邮件不足300秒，还需等待{remaining_time}秒")

                        except Exception as e:
                            print(f"检测合约 {symbol} 长K线时发生错误：{e}")

                except Exception as e:
                    print(f"检查合约 {symbol} K线更新时发生错误：{e}")
                    continue

        # 发送涨跌幅提醒邮件
        if current_price_changes:
            try:
                if len(current_price_changes) == 1:
                    # 单个品种，发送单独邮件
                    symbol, kline, change_percent, change_type = current_price_changes[0]
                    email_content = build_price_change_email_content(symbol, kline, change_percent, change_type)
                    email_subject = f"涨跌幅提醒 - {symbol} {change_type} {change_percent:+.2f}%"
                else:
                    # 多个品种，发送汇总邮件
                    email_content = build_multi_price_change_email_content(current_price_changes)
                    email_subject = f"多品种涨跌幅提醒 - {len(current_price_changes)}个品种"

                # 发送邮件
                send_mail(
                    mail_content=email_content,
                    subject=email_subject,
                    receives=['<EMAIL>']
                )

                # 更新所有相关品种的发送记录
                current_time = time.time()
                for symbol, _, _, _ in current_price_changes:
                    email_send_records[f"{symbol}_price"] = current_time

                print(f"涨跌幅提醒邮件已发送，涉及品种：{[item[0] for item in current_price_changes]}")

            except Exception as e:
                print(f"发送涨跌幅提醒邮件时发生错误：{e}")

        # 发送长K线提醒邮件
        if current_long_candles:
            try:
                if len(current_long_candles) == 1:
                    # 单个品种，发送单独邮件
                    symbol, kline, candle_type, atr_value = current_long_candles[0]
                    email_content = build_long_candle_email_content(symbol, kline, candle_type, atr_value)
                    email_subject = f"长K线提醒 - {symbol} {candle_type}"
                else:
                    # 多个品种，发送汇总邮件
                    email_content = build_multi_symbol_email_content(current_long_candles)
                    email_subject = f"多品种长K线提醒 - {len(current_long_candles)}个品种"

                # 发送邮件
                send_mail(
                    mail_content=email_content,
                    subject=email_subject,
                    receives=['<EMAIL>']
                )

                # 更新所有相关品种的发送记录
                current_time = time.time()
                for symbol, _, _, _ in current_long_candles:
                    email_send_records[f"{symbol}_long"] = current_time

                print(f"长K线提醒邮件已发送，涉及品种：{[item[0] for item in current_long_candles]}")

            except Exception as e:
                print(f"发送长K线提醒邮件时发生错误：{e}")

            # 执行原有的主函数逻辑（可选，根据需要决定是否每次都执行）
            # main(api, T_period_2)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保API连接被正确关闭
        if api is not None:
            try:
                print("正在关闭API连接...")
                api.close()
                print("API连接已关闭")
            except Exception as e:
                print(f"关闭API连接时发生错误: {e}")
