#!/usr/bin/env python3
"""
测试全品种监控功能
验证a.py和b2.py是否能正确处理全部品种而不是只有5个
"""

import os
import pandas as pd
import subprocess
import sys
import time

def create_test_symbols_file():
    """
    创建包含多个品种的测试文件
    """
    print("创建测试品种文件...")
    
    # 创建包含20个测试品种的Excel文件
    test_symbols = [
        "DCE.v2509", "DCE.m2509", "DCE.y2509", "DCE.p2509", "DCE.a2509",
        "SHFE.cu2509", "SHFE.al2509", "SHFE.zn2509", "SHFE.pb2509", "SHFE.ni2509",
        "CZCE.TA509", "CZCE.MA509", "CZCE.CF509", "CZCE.SR509", "CZCE.OI509",
        "SSE.000016", "SSE.000300", "SSE.000905", "SSE.000852", "SSE.510050"
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(test_symbols, columns=['symbol'])
    
    # 保存到Excel文件
    df.to_excel("generated_symbols_9_2025.xlsx", index=False)
    
    print(f"创建了包含 {len(test_symbols)} 个品种的测试文件:")
    for i, symbol in enumerate(test_symbols, 1):
        print(f"  {i:2d}. {symbol}")
    
    return test_symbols

def create_mock_a_script():
    """
    创建模拟的a.py脚本来测试全品种处理
    """
    print("\n创建模拟的a.py脚本...")
    
    mock_a_content = '''#!/usr/bin/env python3
import pandas as pd
import datetime
import os

print("=" * 60)
print("模拟数据获取脚本 (a.py) - 全品种版本")
print("=" * 60)

# 读取合约列表
try:
    symbols_df = pd.read_excel("generated_symbols_9_2025.xlsx")
    if not symbols_df.empty and symbols_df.shape[1] > 0:
        # 获取全部合约代码
        symbol_list = symbols_df.iloc[:, 0].astype(str).tolist()
        print(f"将处理全部 {len(symbol_list)} 个合约: {symbol_list}")
    else:
        print("Excel文件为空或没有列。")
        symbol_list = []
except FileNotFoundError:
    print("错误: generated_symbols_9_2025.xlsx 文件未找到。")
    symbol_list = []
except Exception as e:
    print(f"读取Excel文件时发生错误: {e}")
    symbol_list = []

if not symbol_list:
    print("没有合约可供处理。程序将退出。")
    exit()

# 创建数据目录
base_dir = "data"
today = datetime.datetime.now().strftime('%Y%m%d')
save_dir = os.path.join(base_dir, today)

if not os.path.exists(save_dir):
    os.makedirs(save_dir)

print(f"\\n开始处理 {len(symbol_list)} 个品种...")

# 模拟处理每个品种
for i, symbol in enumerate(symbol_list, 1):
    print(f"处理品种 {i}/{len(symbol_list)}: {symbol}")
    
    # 创建模拟数据
    data = {
        'datetime': [datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
        'open': [100.0],
        'high': [102.0],
        'low': [99.0],
        'close': [101.0],
        'volume': [1000],
        'atr_tr': [2.0],
        'atr': [1.5],
        'boll_upper': [103.0],
        'boll_middle': [101.0],
        'boll_lower': [99.0]
    }
    
    df = pd.DataFrame(data)
    
    # 保存到Excel文件
    filename = f"{symbol.replace('.', '_')}_kline_data.xlsx"
    filepath = os.path.join(save_dir, filename)
    df.to_excel(filepath, index=False)
    
    # 模拟处理时间
    import time
    time.sleep(0.1)  # 每个品种0.1秒

print(f"\\n全部 {len(symbol_list)} 个品种处理完成！")
print(f"数据已保存到: {save_dir}")
'''
    
    with open("a.py", "w", encoding="utf-8") as f:
        f.write(mock_a_content)
    
    print("模拟a.py脚本创建完成")

def create_mock_b1_script():
    """
    创建模拟的b1.py脚本来测试全品种分析
    """
    print("创建模拟的b1.py脚本...")
    
    mock_b1_content = '''#!/usr/bin/env python3
import os
import glob
import datetime

print("=" * 60)
print("模拟K线分析脚本 (b1.py) - 全品种版本")
print("=" * 60)

# 获取数据目录
today = datetime.datetime.now().strftime('%Y%m%d')
data_dir = os.path.join("data", today)

if not os.path.exists(data_dir):
    print(f"数据目录不存在: {data_dir}")
    exit()

# 查找所有Excel文件
excel_files = glob.glob(os.path.join(data_dir, "*.xlsx"))
print(f"找到 {len(excel_files)} 个数据文件")

if not excel_files:
    print("没有找到数据文件")
    exit()

print("\\n开始分析全部品种的倒数第二根K线...")

analyzed_count = 0
abnormal_count = 0

for file_path in excel_files:
    filename = os.path.basename(file_path)
    symbol = filename.replace('_kline_data.xlsx', '').replace('_', '.')
    
    print(f"分析品种: {symbol}")
    analyzed_count += 1
    
    # 模拟分析过程
    import time
    time.sleep(0.05)  # 每个品种0.05秒
    
    # 模拟随机发现异常
    import random
    if random.random() < 0.1:  # 10%概率发现异常
        abnormal_count += 1
        print(f"  -> 发现异常K线")

print(f"\\n分析完成！")
print(f"总共分析: {analyzed_count} 个品种")
print(f"发现异常: {abnormal_count} 个品种")
'''
    
    with open("b1.py", "w", encoding="utf-8") as f:
        f.write(mock_b1_content)
    
    print("模拟b1.py脚本创建完成")

def test_full_symbols_processing():
    """
    测试全品种处理功能
    """
    print("\n" + "=" * 60)
    print("测试全品种处理功能")
    print("=" * 60)
    
    # 测试a.py是否处理全部品种
    print("1. 测试数据获取脚本处理全部品种...")
    try:
        result = subprocess.run([sys.executable, "a.py"], 
                              capture_output=True, text=True, timeout=60)
        
        output = result.stdout
        print("a.py输出:")
        print(output)
        
        # 检查是否处理了全部品种
        if "将处理全部 20 个合约" in output:
            print("✓ a.py正确处理全部20个品种")
            a_success = True
        else:
            print("✗ a.py未正确处理全部品种")
            a_success = False
            
    except Exception as e:
        print(f"a.py测试失败: {e}")
        a_success = False
    
    time.sleep(2)
    
    # 测试b1.py是否分析全部品种
    print("\n2. 测试分析脚本处理全部品种...")
    try:
        result = subprocess.run([sys.executable, "b1.py"], 
                              capture_output=True, text=True, timeout=60)
        
        output = result.stdout
        print("b1.py输出:")
        print(output)
        
        # 检查是否分析了全部品种
        if "找到 20 个数据文件" in output and "总共分析: 20 个品种" in output:
            print("✓ b1.py正确分析全部20个品种")
            b1_success = True
        else:
            print("✗ b1.py未正确分析全部品种")
            b1_success = False
            
    except Exception as e:
        print(f"b1.py测试失败: {e}")
        b1_success = False
    
    return a_success and b1_success

def test_b2_with_full_symbols():
    """
    测试b2.py调度器处理全品种
    """
    print("\n" + "=" * 60)
    print("测试b2.py调度器处理全品种")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, "b2.py", "test"], 
                              capture_output=True, text=True, timeout=120)
        
        output = result.stdout
        print("b2.py测试输出:")
        print(output)
        
        # 检查是否成功执行了任务序列
        success_indicators = [
            "第1步：运行数据获取脚本（全部品种）",
            "第2步：运行K线分析脚本（全部品种）",
            "任务序列执行完成"
        ]
        
        all_found = all(indicator in output for indicator in success_indicators)
        
        if all_found:
            print("✓ b2.py成功执行全品种任务序列")
            return True
        else:
            print("✗ b2.py任务序列执行不完整")
            return False
            
    except Exception as e:
        print(f"b2.py测试失败: {e}")
        return False

def cleanup():
    """
    清理测试文件
    """
    print("\n清理测试文件...")
    
    files_to_remove = [
        "a.py", "b1.py", "generated_symbols_9_2025.xlsx"
    ]
    
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"删除: {file}")
    
    # 清理数据目录
    if os.path.exists("data"):
        import shutil
        shutil.rmtree("data")
        print("删除: data目录")
    
    print("清理完成")

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("全品种监控功能测试")
    print("=" * 60)
    
    try:
        # 创建测试文件
        test_symbols = create_test_symbols_file()
        create_mock_a_script()
        create_mock_b1_script()
        
        # 运行测试
        print(f"\n开始测试处理 {len(test_symbols)} 个品种...")
        
        # 测试单独脚本
        symbols_test = test_full_symbols_processing()
        
        # 测试调度器
        scheduler_test = test_b2_with_full_symbols()
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        print(f"品种数量: {len(test_symbols)} 个")
        print(f"单独脚本测试: {'✓ 通过' if symbols_test else '✗ 失败'}")
        print(f"调度器测试: {'✓ 通过' if scheduler_test else '✗ 失败'}")
        
        if symbols_test and scheduler_test:
            print("\n🎉 全品种监控功能测试通过！")
            print("系统现在可以监控全部品种而不是只有5个")
        else:
            print("\n⚠️  部分测试失败，请检查配置")
    
    finally:
        # 清理测试文件
        cleanup()

if __name__ == "__main__":
    main()
