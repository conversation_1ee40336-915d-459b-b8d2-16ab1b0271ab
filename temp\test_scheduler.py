#!/usr/bin/env python3
"""
测试定时调度器功能
"""

import os
import sys
import time
import datetime
import subprocess

def test_scheduler_commands():
    """
    测试调度器的各种命令
    """
    print("=" * 50)
    print("测试定时调度器功能")
    print("=" * 50)
    
    # 检查文件是否存在
    files_to_check = ["b.py", "a.py"]
    for file_name in files_to_check:
        if os.path.exists(file_name):
            print(f"✓ {file_name} 存在")
        else:
            print(f"✗ {file_name} 不存在")
            if file_name == "a.py":
                print("  警告: a.py 不存在，调度器将无法运行脚本")
    
    print("\n" + "=" * 30)
    print("测试调度器命令")
    print("=" * 30)
    
    # 测试命令列表
    test_commands = [
        ("info", "显示调度信息"),
        ("status", "显示状态信息"),
    ]
    
    for command, description in test_commands:
        print(f"\n测试命令: python b.py {command}")
        print(f"描述: {description}")
        
        try:
            result = subprocess.run(
                [sys.executable, "b.py", command],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print("✓ 命令执行成功")
                if result.stdout:
                    print("输出:")
                    print(result.stdout)
            else:
                print("✗ 命令执行失败")
                if result.stderr:
                    print("错误:")
                    print(result.stderr)
                    
        except subprocess.TimeoutExpired:
            print("✗ 命令执行超时")
        except Exception as e:
            print(f"✗ 命令执行异常: {e}")
    
    print("\n" + "=" * 30)
    print("依赖检查")
    print("=" * 30)
    
    # 检查依赖
    try:
        import schedule
        print("✓ schedule 库已安装")
    except ImportError:
        print("✗ schedule 库未安装")
        print("  请运行: pip install schedule")
        print("  或者运行: python install_requirements.py")
    
    print("\n" + "=" * 30)
    print("时间检查")
    print("=" * 30)
    
    # 显示当前时间和下一个调度时间
    current_time = datetime.datetime.now()
    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 定义调度时间
    schedule_times = [
        "09:00:00", "09:15:00", "09:30:00", "09:45:00", "10:00:00",
        "10:30:00", "10:45:00", "11:00:00", "11:15:00", "13:30:00",
        "13:45:00", "14:00:00", "14:15:00", "14:30:00", "14:45:00",
        "21:00:00", "21:15:00", "21:30:00", "21:45:00", "22:00:00",
        "22:15:00", "22:30:00", "22:45:00"
    ]
    
    # 找到下一个调度时间
    today = current_time.date()
    next_schedule = None
    
    for time_str in schedule_times:
        schedule_time = datetime.datetime.strptime(f"{today} {time_str}", "%Y-%m-%d %H:%M:%S")
        if schedule_time > current_time:
            next_schedule = schedule_time
            break
    
    if next_schedule is None:
        # 如果今天没有更多的调度时间，找明天的第一个
        tomorrow = today + datetime.timedelta(days=1)
        next_schedule = datetime.datetime.strptime(f"{tomorrow} {schedule_times[0]}", "%Y-%m-%d %H:%M:%S")
    
    print(f"下次调度时间: {next_schedule.strftime('%Y-%m-%d %H:%M:%S')}")
    
    time_diff = next_schedule - current_time
    hours, remainder = divmod(time_diff.total_seconds(), 3600)
    minutes, seconds = divmod(remainder, 60)
    print(f"距离下次调度: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    
    print("\n" + "=" * 30)
    print("使用建议")
    print("=" * 30)
    
    print("1. 启动调度器: python b.py")
    print("2. 测试运行: python b.py test")
    print("3. 查看状态: python b.py status")
    print("4. 查看信息: python b.py info")
    print("5. 停止调度器: 按 Ctrl+C")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_scheduler_commands()
