#!/usr/bin/env python3
"""
测试只使用ATR和BOLL条件的K线分析功能
验证涨跌幅条件已被禁用
"""

import os
import pandas as pd
import numpy as np
import datetime
from b1 import KLineAnalyzer

def create_test_data_with_different_conditions():
    """
    创建测试数据，包含不同类型的异常情况
    """
    print("创建测试数据（包含不同异常类型）...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    test_dir = f"test_conditions/{today}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    
    test_cases = [
        {
            "symbol": "TEST.price_change",
            "description": "大涨跌幅但ATR和BOLL正常",
            "price_change": 3.0,  # 大涨跌幅
            "atr_ratio": 1.0,     # ATR正常
            "boll_position": 0.5  # BOLL正常
        },
        {
            "symbol": "TEST.atr_exceed", 
            "description": "ATR超标但涨跌幅和BOLL正常",
            "price_change": 0.3,  # 小涨跌幅
            "atr_ratio": 2.5,     # ATR超标
            "boll_position": 0.5  # BOLL正常
        },
        {
            "symbol": "TEST.boll_breakthrough",
            "description": "BOLL突破但涨跌幅和ATR正常", 
            "price_change": 0.2,  # 小涨跌幅
            "atr_ratio": 1.0,     # ATR正常
            "boll_position": 0.98 # BOLL突破上轨
        },
        {
            "symbol": "TEST.normal",
            "description": "所有条件都正常",
            "price_change": 0.1,  # 小涨跌幅
            "atr_ratio": 0.8,     # ATR正常
            "boll_position": 0.6  # BOLL正常
        }
    ]
    
    for case in test_cases:
        print(f"生成 {case['symbol']}: {case['description']}")
        
        # 生成30根K线数据
        n_bars = 30
        base_price = 100.0
        
        # 前28根K线正常变化
        normal_changes = np.random.normal(0, 0.1, n_bars - 2)
        
        # 倒数第二根K线设置为测试条件
        second_last_change = case['price_change']
        
        # 最后一根K线正常
        last_change = 0.05
        
        price_changes = np.append(normal_changes, [second_last_change, last_change])
        
        # 生成OHLC数据
        opens, highs, lows, closes, volumes = [], [], [], [], []
        current_price = base_price
        
        for j in range(n_bars):
            open_price = current_price
            change = price_changes[j]
            close_price = open_price * (1 + change / 100)
            
            if change > 0:
                high_price = close_price * (1 + np.random.uniform(0, 0.1) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.05) / 100)
            else:
                high_price = open_price * (1 + np.random.uniform(0, 0.05) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.1) / 100)
            
            volume = np.random.randint(1000, 5000)
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 计算ATR指标（人为调整倒数第二根）
        atr_tr_values = []
        atr_values = []
        
        for j in range(n_bars):
            if j == 0:
                atr_tr = highs[j] - lows[j]
                atr = atr_tr
            else:
                tr1 = highs[j] - lows[j]
                tr2 = abs(highs[j] - closes[j-1])
                tr3 = abs(lows[j] - closes[j-1])
                atr_tr = max(tr1, tr2, tr3)
                
                if j < 14:
                    atr = sum(atr_tr_values + [atr_tr]) / (j + 1)
                else:
                    atr = sum(atr_tr_values[-13:] + [atr_tr]) / 14
            
            atr_tr_values.append(round(atr_tr, 4))
            atr_values.append(round(atr, 4))
        
        # 人为调整倒数第二根K线的ATR，使其符合测试条件
        target_atr = abs(closes[-2] - opens[-2]) / case['atr_ratio']
        atr_values[-2] = round(target_atr, 4)
        
        # 计算BOLL指标（人为调整倒数第二根）
        boll_upper, boll_middle, boll_lower = [], [], []
        
        for j in range(n_bars):
            if j < 20:
                middle = closes[j]
                std = 0.5
            else:
                period_closes = closes[max(0, j-19):j+1]
                middle = sum(period_closes) / len(period_closes)
                variance = sum((x - middle) ** 2 for x in period_closes) / len(period_closes)
                std = variance ** 0.5
            
            upper = middle + 2 * std
            lower = middle - 2 * std
            
            boll_upper.append(round(upper, 2))
            boll_middle.append(round(middle, 2))
            boll_lower.append(round(lower, 2))
        
        # 人为调整倒数第二根K线的BOLL位置
        boll_range = boll_upper[-2] - boll_lower[-2]
        target_close = boll_lower[-2] + boll_range * case['boll_position']
        closes[-2] = round(target_close, 2)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': pd.date_range('2025-01-09 09:00:00', periods=n_bars, freq='15min'),
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'atr_tr': atr_tr_values,
            'atr': atr_values,
            'boll_upper': boll_upper,
            'boll_middle': boll_middle,
            'boll_lower': boll_lower
        })
        
        # 保存到Excel文件
        file_name = f"{case['symbol'].replace('.', '_')}_kline_indicators.xlsx"
        file_path = os.path.join(test_dir, file_name)
        df.to_excel(file_path, index=False)
        
        # 显示倒数第二根K线信息
        second_last_row = df.iloc[-2]
        actual_change = ((second_last_row['close'] - second_last_row['open']) / second_last_row['open']) * 100
        actual_atr_ratio = abs(second_last_row['close'] - second_last_row['open']) / second_last_row['atr']
        actual_boll_pos = (second_last_row['close'] - second_last_row['boll_lower']) / (second_last_row['boll_upper'] - second_last_row['boll_lower'])
        
        print(f"  实际涨跌幅: {actual_change:+.2f}%")
        print(f"  实际ATR比值: {actual_atr_ratio:.2f}")
        print(f"  实际BOLL位置: {actual_boll_pos:.2f}")
    
    print(f"\n测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_condition_filtering():
    """
    测试条件过滤功能
    """
    print("=" * 60)
    print("测试ATR+BOLL条件过滤功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_test_data_with_different_conditions()
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="test_conditions")
    
    # 设置阈值
    analyzer.price_change_threshold = 1.0  # 1%（应该被忽略）
    analyzer.atr_multiplier = 2.0          # 2倍ATR
    analyzer.boll_threshold = 0.95         # 95%位置
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  涨跌幅阈值: {analyzer.price_change_threshold}% (已禁用)")
    print(f"  ATR倍数阈值: {analyzer.atr_multiplier}")
    print(f"  BOLL突破阈值: {analyzer.boll_threshold}")
    
    # 运行分析
    print(f"\n开始分析...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n测试结果:")
    print(f"  异常品种总数: {result['total_abnormal']}")
    print(f"  ATR超标: {result['atr_exceeded_count']} 个品种")
    print(f"  BOLL突破: {result['boll_breakthrough_count']} 个品种")
    print(f"  涨跌幅超标: {result['price_change_count']} 个品种 (应该为0)")
    
    # 验证结果
    expected_results = {
        "TEST.price_change": False,    # 只有涨跌幅异常，应该不被检测
        "TEST.atr_exceed": True,       # ATR超标，应该被检测
        "TEST.boll_breakthrough": True, # BOLL突破，应该被检测
        "TEST.normal": False           # 正常，应该不被检测
    }
    
    detected_symbols = result['symbols']
    
    print(f"\n验证结果:")
    all_correct = True
    
    for symbol, should_detect in expected_results.items():
        is_detected = symbol in detected_symbols
        status = "✓" if is_detected == should_detect else "✗"
        print(f"  {status} {symbol}: {'检测到' if is_detected else '未检测'} ({'预期' if should_detect else '正常'})")
        
        if is_detected != should_detect:
            all_correct = False
    
    # 验证涨跌幅条件确实被禁用
    price_change_triggered = result['price_change_count'] == 0
    print(f"  {'✓' if price_change_triggered else '✗'} 涨跌幅条件已禁用: {result['price_change_count']} 个触发")
    
    if not price_change_triggered:
        all_correct = False
    
    # 显示详细的异常K线信息
    if result['abnormal_klines']:
        print(f"\n异常K线详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     ATR比值: {kline['body_atr_ratio']:.2f}")
            print(f"     BOLL位置: {kline['boll_position']:.2f}")
            print(f"     触发原因: {'; '.join(kline['trigger_reason'])}")
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_conditions"):
        shutil.rmtree("test_conditions")
        print(f"\n测试数据已清理")
    
    return all_correct

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("ATR+BOLL条件测试（涨跌幅条件禁用验证）")
    print("=" * 60)
    
    try:
        # 运行测试
        success = test_condition_filtering()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if success:
            print("🎉 所有测试通过！")
            print("✓ 涨跌幅条件已成功禁用")
            print("✓ ATR条件正常工作")
            print("✓ BOLL条件正常工作")
            print("✓ 条件过滤逻辑正确")
        else:
            print("⚠️  部分测试失败")
            print("请检查条件过滤逻辑")
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
