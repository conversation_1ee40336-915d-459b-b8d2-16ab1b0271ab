#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断邮件图片显示问题
"""

import os
import pandas as pd
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from b1_macd_1 import KLineAnalyzer

def test_email_image_display():
    """
    测试邮件图片显示问题
    """
    print("=" * 60)
    print("诊断邮件图片显示问题")
    print("=" * 60)
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="data", kline_period="30min")
    
    # 读取测试数据
    data_files = [f for f in os.listdir(analyzer.target_path) if f.endswith('.xlsx')]
    if not data_files:
        print("未找到数据文件")
        return False
    
    test_file = data_files[0]
    file_path = os.path.join(analyzer.target_path, test_file)
    df = pd.read_excel(file_path)
    symbol = test_file.replace('_kline_data.xlsx', '').replace('_', '.')
    
    print(f"使用测试数据: {symbol}")
    print(f"K线数量: {len(df)}")
    
    # 创建测试信号
    test_kline = {
        'symbol': symbol,
        'datetime': df.iloc[-5]['datetime'],
        'open': df.iloc[-5]['open'],
        'high': df.iloc[-5]['high'],
        'low': df.iloc[-5]['low'],
        'close': df.iloc[-5]['close'],
        'volume': df.iloc[-5]['volume'],
        'price_change': 1.5,
        'change_type': '做空',
        'macd_histogram': -2.5,
        'entry_index': -5,
        'signal_type': 'MACDSHORT信号',
        'direction': 'SHORT',
        'current_peak_idx': -8,
        'prev_peak_idx': -25,
        'trigger_reason': ['MACD顶背离信号测试'],
        'kline_data': df.copy()
    }
    
    print(f"\n步骤1: 测试图片生成...")
    
    # 直接测试图片生成
    chart_base64 = analyzer.create_kline_chart(
        df, symbol, 
        abnormal_index=-5, 
        peak_indices=[-8, -25]
    )
    
    if chart_base64:
        print(f"✓ 图片生成成功")
        print(f"  Base64长度: {len(chart_base64)} 字符")
        print(f"  前50字符: {chart_base64[:50]}...")
        print(f"  后50字符: ...{chart_base64[-50:]}")
        
        # 验证base64格式
        try:
            import base64
            decoded = base64.b64decode(chart_base64)
            print(f"  解码后大小: {len(decoded)} 字节")
            png_signature = b'\x89PNG\r\n\x1a\n'
            print(f"  PNG头部检查: {'✓' if decoded.startswith(png_signature) else '✗'}")
        except Exception as e:
            print(f"  Base64解码失败: {e}")
    else:
        print("✗ 图片生成失败")
        return False
    
    print(f"\n步骤2: 测试邮件内容生成...")
    
    # 生成邮件内容
    email_content = analyzer.build_email_content([test_kline])
    
    # 检查邮件内容
    has_img_tag = '<img src="data:image/png;base64,' in email_content
    img_count = email_content.count('<img src="data:image/png;base64,')
    base64_count = email_content.count('data:image/png;base64,')
    
    print(f"  邮件内容长度: {len(email_content)} 字符")
    print(f"  包含img标签: {'✓' if has_img_tag else '✗'}")
    print(f"  img标签数量: {img_count}")
    print(f"  base64图片数量: {base64_count}")
    
    # 保存邮件内容
    with open("test_email_debug.html", "w", encoding="utf-8") as f:
        f.write(email_content)
    print(f"  邮件内容已保存到: test_email_debug.html")
    
    print(f"\n步骤3: 创建简化的测试邮件...")
    
    # 创建简化的测试邮件
    simple_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>邮件图片显示测试</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .test-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
        .chart-image {{ max-width: 100%; height: auto; border: 1px solid #ccc; }}
    </style>
</head>
<body>
    <h1>邮件图片显示测试</h1>
    
    <div class="test-section">
        <h3>测试1: 内联Base64图片</h3>
        <p>这是一个内联的Base64编码图片：</p>
        <img src="data:image/png;base64,{chart_base64}" class="chart-image" alt="测试图片">
    </div>
    
    <div class="test-section">
        <h3>测试2: 图片信息</h3>
        <p><strong>Base64长度:</strong> {len(chart_base64)} 字符</p>
        <p><strong>预估大小:</strong> {len(chart_base64) * 3 // 4 // 1024:.1f} KB</p>
        <p><strong>格式:</strong> PNG</p>
    </div>
    
    <div class="test-section">
        <h3>测试3: 常见问题排查</h3>
        <ul>
            <li>Base64编码是否正确: {'✓' if chart_base64 else '✗'}</li>
            <li>图片大小是否合理: {'✓' if len(chart_base64) < 500000 else '✗ (过大)'}</li>
            <li>MIME类型是否正确: ✓ (data:image/png;base64,)</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>测试4: 邮件客户端兼容性说明</h3>
        <p><strong>支持内联图片的邮件客户端:</strong></p>
        <ul>
            <li>✓ Gmail (网页版)</li>
            <li>✓ Outlook (网页版)</li>
            <li>✓ Apple Mail</li>
            <li>✓ Thunderbird</li>
        </ul>
        <p><strong>可能不支持的邮件客户端:</strong></p>
        <ul>
            <li>✗ 某些企业邮件客户端（安全策略限制）</li>
            <li>✗ 旧版本的Outlook桌面版</li>
            <li>✗ 某些移动邮件应用</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>解决方案建议</h3>
        <ol>
            <li><strong>如果在浏览器中能看到图片:</strong> 说明图片生成正常，问题在邮件客户端</li>
            <li><strong>如果邮件客户端不显示:</strong> 尝试使用附件方式发送图片</li>
            <li><strong>企业邮箱:</strong> 可能需要IT管理员调整安全策略</li>
            <li><strong>移动设备:</strong> 尝试在电脑上查看邮件</li>
        </ol>
    </div>
</body>
</html>
"""
    
    with open("test_email_simple.html", "w", encoding="utf-8") as f:
        f.write(simple_html)
    print(f"  简化测试邮件已保存到: test_email_simple.html")
    
    print(f"\n步骤4: 测试邮件发送（模拟）...")
    
    # 模拟邮件发送测试
    try:
        # 创建邮件对象
        msg = MIMEMultipart('alternative')
        msg['Subject'] = "MACD背离信号测试邮件"
        msg['From'] = "<EMAIL>"
        msg['To'] = "<EMAIL>"
        
        # 添加HTML内容
        html_part = MIMEText(email_content, 'html', 'utf-8')
        msg.attach(html_part)
        
        # 获取邮件大小
        email_size = len(msg.as_string())
        print(f"  邮件总大小: {email_size // 1024:.1f} KB")
        print(f"  大小检查: {'✓' if email_size < 10 * 1024 * 1024 else '✗ (超过10MB)'}")
        
        print(f"  邮件格式检查: ✓")
        
    except Exception as e:
        print(f"  邮件格式检查失败: {e}")
    
    return True

def main():
    """
    主函数
    """
    print("=" * 60)
    print("邮件图片显示问题诊断工具")
    print("=" * 60)
    
    try:
        success = test_email_image_display()
        
        print("\n" + "=" * 60)
        print("诊断结果")
        print("=" * 60)
        
        if success:
            print("🔍 诊断完成！")
            print("\n📁 生成的测试文件:")
            print("  - test_email_debug.html (完整邮件内容)")
            print("  - test_email_simple.html (简化测试)")
            
            print("\n🔧 排查步骤:")
            print("  1. 用浏览器打开 test_email_simple.html")
            print("  2. 如果能看到图片，说明图片生成正常")
            print("  3. 如果邮件中看不到图片，可能是邮件客户端限制")
            
            print("\n💡 解决方案:")
            print("  - 使用支持HTML邮件的客户端（Gmail、Outlook网页版）")
            print("  - 检查邮件客户端的图片显示设置")
            print("  - 考虑使用附件方式发送图片")
            print("  - 联系IT管理员调整邮件安全策略")
        else:
            print("❌ 诊断失败，请检查数据文件")
    
    except Exception as e:
        print(f"诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
