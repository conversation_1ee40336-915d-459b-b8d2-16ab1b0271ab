# MACD背离信号分析器 (b1.py) 使用说明

## 概述

b1.py 是一个专业的MACD背离信号分析器，专为**30分钟K线周期**设计，通过检测MACD背离信号并确认入场点，发送包含**K线图+MACD柱状图**的可视化邮件提醒。

## 功能特点

- ✅ **MACD背离检测**：专业的MACD背离信号识别算法
- ✅ **入场点确认**：MACD柱小于0且绝对值减小时触发提醒
- ✅ **30分钟K线周期**：专为30分钟K线数据设计和优化
- ✅ **双图表显示**：上方K线图+下方MACD柱状图
- ✅ **50根历史K线**：提供充足的技术分析上下文
- ✅ **全品种监控**：处理Excel文件中的全部品种
- ✅ **可视化标记**：黄色高亮标记入场点位置
- ✅ **时间轴显示**：K线图包含时间信息，便于定位
- ✅ **响应式邮件**：美观的HTML邮件格式，适配各种设备

## 检测原理

### ATR指标
- **ATR (Average True Range)**：平均真实波幅，衡量价格波动性
- **计算周期**：默认14根K线
- **用途**：作为判断K线异常的基准

### BOLL指标
- **BOLL (Bollinger Bands)**：布林带，包含上轨、中轨、下轨
- **用途**：判断价格是否突破正常波动范围
- **突破判断**：价格接近上轨或下轨时触发

### MACD背离信号检测条件

程序使用专业的MACD背离分析算法：

1. **背离检测**：MACD波峰抬升 + 价格下降（底背离）
2. **入场确认**：MACD柱 < 0 且绝对值减小
3. **时间窗口**：在背离点后最多搜索10根K线寻找入场点

### 技术原理
- **MACD参数**：快线12，慢线26，信号线9（标准设置）
- **波峰识别**：检测MACD柱在零线下方的局部最小值
- **背离确认**：比较两个波峰的MACD值和对应价格
- **入场时机**：MACD柱绝对值开始减小时触发提醒

### 默认参数
- **K线周期**：30分钟
- **K线图历史数量**：50根K线
- **MACD快线周期**：12
- **MACD慢线周期**：26
- **MACD信号线周期**：9
- **背离搜索范围**：最多50根K线
- **入场搜索范围**：最多10根K线

## 数据格式要求

### 目录结构
```
data/
└── 20250109/                    # 当日日期目录（自动使用当日）
    ├── DCE_v2509_kline_indicators.xlsx
    ├── SSE_000016_kline_indicators.xlsx
    ├── SSE_000300_kline_indicators.xlsx
    └── ...
```

### Excel文件格式
Excel文件必须包含以下列：

**基础K线数据：**
- **datetime**: 时间
- **open**: 开盘价
- **high**: 最高价
- **low**: 最低价
- **close**: 收盘价
- **volume**: 成交量

**技术指标数据：**
- **atr_tr**: ATR真实波幅
- **atr**: ATR值
- **boll_upper**: BOLL上轨
- **boll_middle**: BOLL中轨
- **boll_lower**: BOLL下轨

## 使用方法

### 1. 基本用法
```bash
# 分析当日数据并发送邮件（自动使用当日日期）
python b1.py

# 分析指定日期的数据
python b1.py 20250109

# 分析但不发送邮件
python b1.py 20250109 --no-email
```

### 2. 测试模式
```bash
# 测试模式（不发送邮件）
python b1.py test

# 测试分析器功能
python test_b1.py
```

### 3. 帮助信息
```bash
python b1.py help
```

## 配置参数

可以在代码中修改以下参数：

```python
class KLineAnalyzer:
    def __init__(self):
        self.atr_period = 14              # ATR周期
        self.atr_multiplier = 2.0         # ATR倍数阈值
        self.price_change_threshold = 0.5  # 涨跌幅阈值(%)
```

## 邮件报告（MACD专业版）

### 邮件内容包含
- **汇总统计**：MACD背离信号总数、上涨下跌分布
- **详细信息**：每个信号品种的完整信息
- **入场时间**：精确的入场点时间
- **双图表显示**：K线图+MACD柱状图
- **入场标记**：图表中黄色高亮标记入场点位置
- **MACD数据**：MACD柱值、入场点索引等
- **信号类型**：MACD背离信号分类

### 邮件格式特点
- **响应式设计**：适配桌面和移动设备
- **分区展示**：每个异常品种独立展示区域
- **颜色区分**：上涨绿色，下跌红色
- **图表嵌入**：K线图直接嵌入邮件内容
- **专业布局**：清晰的信息层次和视觉效果

### 图表特点
- **双图表布局**：上方K线图（3:1比例）+ 下方MACD柱状图（1:1比例）
- **50根历史K线**：显示充足的历史上下文信息
- **30分钟周期**：适配30分钟K线数据的时间轴
- **MACD柱状图**：红绿柱状图显示MACD柱值变化
- **入场标记**：黄色边框突出显示入场点K线和对应MACD柱
- **箭头指示**：红色箭头和文字标注入场位置
- **时间轴显示**：顶部显示具体时间点（HH:MM格式）
- **网格背景**：便于读取价格、时间和MACD数值
- **高分辨率**：15x12英寸大图，清晰显示细节

## 日志文件

- **文件名**: `kline_analyzer.log`
- **内容**: 详细的分析过程和结果
- **格式**: 时间戳 + 日志级别 + 消息

## 输出示例

```
============================================================
K线异常波动分析器
============================================================
2025-01-09 10:30:00 - INFO - K线分析器初始化完成
2025-01-09 10:30:00 - INFO - 数据目录: D:\...\temp\data\20250603
2025-01-09 10:30:00 - INFO - 找到 5 个Excel文件
2025-01-09 10:30:01 - INFO - DCE.v2509: 发现异常K线 - 上涨 +2.15%, ATR比值: 2.3
2025-01-09 10:30:01 - INFO - 异常K线提醒邮件发送成功，共 3 个异常K线

========================================
最终结果
========================================
分析日期: 20250603
异常K线总数: 3
上涨异常: 2 个
下跌异常: 1 个
涉及品种数: 2
✓ 邮件发送成功
========================================
```

## 故障排除

### 问题1：找不到数据文件
**现象**: "目录中没有找到Excel文件"
**解决方案**:
1. 检查数据目录路径是否正确
2. 确认Excel文件存在且不是临时文件(~$开头)
3. 检查文件权限

### 问题2：数据格式错误
**现象**: "缺少必要列"
**解决方案**:
1. 确认Excel文件包含open, high, low, close列
2. 检查列名是否正确（区分大小写）
3. 确认数据不为空

### 问题3：邮件发送失败
**现象**: "发送邮件时发生错误"
**解决方案**:
1. 检查网络连接
2. 确认send_mail.py配置正确
3. 检查邮箱设置和密码

### 问题4：没有检测到异常K线
**现象**: "未发现异常K线"
**解决方案**:
1. 降低阈值参数进行测试
2. 检查数据是否包含足够的K线（至少14根）
3. 确认价格数据的合理性

## 高级用法

### 自定义阈值
```python
analyzer = KLineAnalyzer(target_date="20250603")
analyzer.price_change_threshold = 1.0  # 1%涨跌幅阈值
analyzer.atr_multiplier = 1.5          # 1.5倍ATR阈值
result = analyzer.run_analysis()
```

### 批量分析多个日期
```python
dates = ["20250601", "20250602", "20250603"]
for date in dates:
    analyzer = KLineAnalyzer(target_date=date)
    result = analyzer.run_analysis(send_email=False)
    print(f"{date}: {result['total_abnormal']} 个异常")
```

## 依赖要求

### Python包依赖
```bash
pip install pandas numpy matplotlib openpyxl
```

### 具体版本要求
- **pandas**: 用于数据处理和Excel文件读取
- **numpy**: 用于数值计算
- **matplotlib**: 用于K线图绘制
- **openpyxl**: 用于Excel文件操作

### 字体要求（可选）
为了K线图中文显示正常，建议安装中文字体：
- Windows: 系统自带SimHei字体
- macOS: 安装Arial Unicode MS
- Linux: 安装中文字体包

## 测试功能

### 基础测试
```bash
# 测试ATR+BOLL条件过滤
python test_atr_boll_only.py

# 测试邮件图表功能
python test_email_with_charts.py
```

### 邮件预览
运行测试后会生成`test_email_preview.html`文件，可用浏览器打开预览邮件效果。

## 技术支持

如遇到问题，请：
1. 查看 `kline_analyzer.log` 日志文件
2. 使用 `python b1.py test` 测试功能
3. 运行 `python test_b1.py` 进行完整测试
4. 检查matplotlib是否正确安装和配置
