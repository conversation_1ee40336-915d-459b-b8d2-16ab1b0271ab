# 定时任务调度器使用说明

## 概述

b.py 是一个定时任务调度器，用于在每天的固定时间点自动运行 a.py 脚本。

## 功能特点

- ✅ 每天23个固定时间点自动运行脚本
- ✅ 进程管理：自动终止之前运行的任务
- ✅ 日志记录：详细的运行日志
- ✅ 异常处理：网络断开、脚本错误等
- ✅ 手动测试：支持手动运行脚本测试
- ✅ 状态监控：查看当前运行状态

## 运行时间表

每天在以下时间点自动运行 a.py：

### 上午时段
- 09:00:00
- 09:15:00
- 09:30:00
- 09:45:00
- 10:00:00
- 10:30:00
- 10:45:00
- 11:00:00
- 11:15:00

### 下午时段
- 13:30:00
- 13:45:00
- 14:00:00
- 14:15:00
- 14:30:00
- 14:45:00

### 夜间时段
- 21:00:00
- 21:15:00
- 21:30:00
- 21:45:00
- 22:00:00
- 22:15:00
- 22:30:00
- 22:45:00

## 安装依赖

### 方法1：自动安装
```bash
python install_requirements.py
```

### 方法2：手动安装
```bash
pip install schedule
```

## 使用方法

### 1. 启动定时调度器
```bash
python b.py
```
这将启动调度器，按照时间表自动运行脚本。

### 2. 手动测试运行
```bash
python b.py test
```
立即运行一次 a.py 脚本，用于测试。

### 3. 查看状态信息
```bash
python b.py status
```
显示当前脚本运行状态和下次运行时间。

### 4. 查看调度信息
```bash
python b.py info
```
显示所有定时任务的详细信息。

### 5. 停止调度器
按 `Ctrl+C` 停止调度器。

## 日志文件

- **scheduler.log**: 详细的运行日志
- 日志包含：任务启动时间、进程ID、错误信息等

## 文件结构

```
temp/
├── a.py                    # 主要的交易脚本
├── b.py                    # 定时调度器
├── install_requirements.py # 依赖安装脚本
├── scheduler.log           # 运行日志（自动生成）
└── README_scheduler.md     # 本说明文档
```

## 注意事项

1. **确保 a.py 存在**：调度器会检查 a.py 是否存在
2. **进程管理**：如果上一个任务还在运行，新任务会先终止旧任务
3. **网络连接**：确保网络连接稳定，a.py 需要连接天勤API
4. **权限问题**：确保有足够权限运行脚本和创建日志文件

## 故障排除

### 问题1：提示缺少 schedule 库
**解决方案**：
```bash
pip install schedule
# 或者
python install_requirements.py
```

### 问题2：a.py 脚本运行失败
**解决方案**：
1. 检查 a.py 是否存在
2. 手动运行 `python a.py` 测试
3. 查看 scheduler.log 日志文件

### 问题3：调度器意外停止
**解决方案**：
1. 查看 scheduler.log 日志
2. 检查系统资源（内存、磁盘空间）
3. 重新启动调度器

## 高级配置

如需修改运行时间，编辑 b.py 中的 `schedule_times` 列表：

```python
self.schedule_times = [
    "09:00:00",
    "09:15:00",
    # 添加或修改时间点
]
```

## 监控建议

1. **定期检查日志**：查看 scheduler.log 确保正常运行
2. **监控磁盘空间**：日志文件会逐渐增大
3. **备份重要数据**：定期备份交易数据和配置文件

## 技术支持

如遇到问题，请：
1. 查看 scheduler.log 日志文件
2. 使用 `python b.py status` 检查状态
3. 尝试 `python b.py test` 手动测试
