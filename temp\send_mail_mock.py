#!/usr/bin/env python3
"""
模拟邮件发送函数（用于测试）
"""

def send_mail(mail_content, subject, receives):
    """
    模拟邮件发送函数
    
    参数:
    mail_content: 邮件内容
    subject: 邮件主题
    receives: 接收者列表
    """
    print("=" * 50)
    print("模拟邮件发送")
    print("=" * 50)
    print(f"收件人: {receives}")
    print(f"主题: {subject}")
    print(f"内容长度: {len(mail_content)} 字符")
    print("邮件发送成功（模拟）")
    print("=" * 50)
    
    # 保存邮件内容到文件
    with open("last_email_content.html", "w", encoding="utf-8") as f:
        f.write(mail_content)
    print("邮件内容已保存到: last_email_content.html")
