#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件图片显示功能
"""

import os
import pandas as pd
import numpy as np
import datetime
from b1_macd_1 import KLineAnalyzer

def create_test_data_with_macd_signal():
    """
    创建包含明确MACD背离信号的测试数据
    """
    print("创建包含MACD背离信号的测试数据...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    test_dir = f"test_email_chart/{today}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    
    symbol = "TEST.email_chart"
    print(f"生成 {symbol} 的数据...")
    
    # 生成80根30分钟K线数据
    n_bars = 80
    base_price = 100.0
    
    # 创建特定的价格和MACD模式以确保触发背离信号
    prices = []
    current_price = base_price
    
    # 前60根K线：正常波动
    for i in range(60):
        change = np.random.normal(0, 0.2)
        current_price = current_price * (1 + change / 100)
        prices.append(current_price)
    
    # 第61-70根K线：价格下跌，MACD也下跌（第一个波峰）
    for i in range(10):
        change = np.random.normal(-0.5, 0.1)
        current_price = current_price * (1 + change / 100)
        prices.append(current_price)
    
    # 第71-80根K线：价格继续下跌但幅度减小，MACD开始改善（第二个波峰，形成背离）
    for i in range(10):
        change = np.random.normal(-0.2, 0.05)
        current_price = current_price * (1 + change / 100)
        prices.append(current_price)
    
    # 生成OHLC数据
    opens, highs, lows, closes, volumes = [], [], [], [], []
    
    for i, price in enumerate(prices):
        open_price = price
        close_price = price * (1 + np.random.normal(0, 0.1) / 100)
        
        if close_price >= open_price:
            high_price = close_price * (1 + np.random.uniform(0, 0.1) / 100)
            low_price = open_price * (1 - np.random.uniform(0, 0.05) / 100)
        else:
            high_price = open_price * (1 + np.random.uniform(0, 0.05) / 100)
            low_price = close_price * (1 - np.random.uniform(0, 0.1) / 100)
        
        volume = np.random.randint(5000, 20000)
        
        opens.append(round(open_price, 2))
        highs.append(round(high_price, 2))
        lows.append(round(low_price, 2))
        closes.append(round(close_price, 2))
        volumes.append(volume)
    
    # 创建30分钟时间序列
    start_time = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    time_list = []
    current_time = start_time
    
    for j in range(n_bars):
        time_list.append(current_time)
        current_time += datetime.timedelta(minutes=30)
        
        # 跳过非交易时间（简化处理）
        if current_time.hour == 12:
            current_time = current_time.replace(hour=13, minute=30)
        elif current_time.hour >= 15 and current_time.hour < 21:
            current_time = current_time.replace(hour=21, minute=0)
        elif current_time.hour >= 24:
            current_time = current_time.replace(hour=9, minute=0) + datetime.timedelta(days=1)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'datetime': time_list,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })
    
    # 保存到Excel文件
    file_name = f"{symbol.replace('.', '_')}_kline_indicators.xlsx"
    file_path = os.path.join(test_dir, file_name)
    df.to_excel(file_path, index=False)
    
    print(f"  文件: {file_name}")
    print(f"  K线数量: {len(df)} 根")
    print(f"  时间范围: {df.iloc[0]['datetime']} 到 {df.iloc[-1]['datetime']}")
    print(f"  价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    print(f"\n测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_email_chart_generation():
    """
    测试邮件图表生成功能
    """
    print("=" * 60)
    print("测试邮件图表生成功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_test_data_with_macd_signal()
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="test_email_chart", kline_period="30min")
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  K线周期: {analyzer.kline_period}")
    print(f"  K线图历史数量: {analyzer.chart_history_bars} 根")
    
    # 手动创建一个测试信号来验证图表生成
    print(f"\n手动创建测试信号...")
    
    # 读取测试数据
    test_files = [f for f in os.listdir(analyzer.target_path) if f.endswith('.xlsx')]
    if not test_files:
        print("未找到测试数据文件")
        return False
    
    file_path = os.path.join(analyzer.target_path, test_files[0])
    df = pd.read_excel(file_path)
    
    # 计算MACD指标
    macd_line, signal_line, macd_histogram = analyzer.calculate_macd(df)
    df['macd_line'] = macd_line
    df['signal_line'] = signal_line
    df['macd_histogram'] = macd_histogram
    
    # 创建一个模拟的异常K线信号
    test_kline = {
        'symbol': 'TEST.email_chart',
        'datetime': df.iloc[-5]['datetime'],  # 倒数第5根K线作为入场点
        'open': df.iloc[-5]['open'],
        'high': df.iloc[-5]['high'],
        'low': df.iloc[-5]['low'],
        'close': df.iloc[-5]['close'],
        'volume': df.iloc[-5]['volume'],
        'price_change': 0.5,
        'change_type': '上涨',
        'macd_histogram': macd_histogram.iloc[-5],
        'entry_index': -5,  # 倒数第5根K线
        'signal_type': 'MACD背离入场',
        'trigger_reason': ['MACD背离信号测试'],
        'kline_data': df.copy()
    }
    
    print(f"测试信号创建完成:")
    print(f"  品种: {test_kline['symbol']}")
    print(f"  入场时间: {test_kline['datetime']}")
    print(f"  入场点索引: {test_kline['entry_index']}")
    print(f"  MACD柱值: {test_kline['macd_histogram']:.4f}")
    
    # 生成邮件内容
    print(f"\n生成邮件内容...")
    email_content = analyzer.build_email_content([test_kline])
    
    # 保存邮件内容到HTML文件
    html_file = "test_email_chart_preview.html"
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(email_content)
    
    print(f"邮件内容已保存到: {html_file}")
    
    # 检查邮件内容是否包含图片
    has_chart = "data:image/png;base64," in email_content
    chart_count = email_content.count("data:image/png;base64,")
    
    print(f"\n图表检查结果:")
    print(f"  包含图表: {'是' if has_chart else '否'}")
    print(f"  图表数量: {chart_count}")
    
    if has_chart:
        # 检查base64数据长度
        import re
        base64_matches = re.findall(r'data:image/png;base64,([A-Za-z0-9+/=]+)', email_content)
        if base64_matches:
            base64_length = len(base64_matches[0])
            print(f"  Base64数据长度: {base64_length} 字符")
            print(f"  预估图片大小: {base64_length * 3 // 4 // 1024:.1f} KB")
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_email_chart"):
        shutil.rmtree("test_email_chart")
        print(f"\n测试数据已清理")
    
    return has_chart

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("邮件图表功能测试")
    print("=" * 60)
    
    try:
        # 检查依赖
        try:
            import matplotlib.pyplot as plt
            print("✓ matplotlib 可用")
        except ImportError:
            print("✗ matplotlib 不可用，请安装: pip install matplotlib")
            return
        
        # 运行测试
        success = test_email_chart_generation()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if success:
            print("🎉 邮件图表功能测试成功！")
            print("✓ K线图生成正常")
            print("✓ MACD柱状图生成正常")
            print("✓ 入场点标记正常")
            print("✓ Base64编码正常")
            print("✓ 邮件HTML格式正确")
            print("\n请用浏览器打开 test_email_chart_preview.html 查看效果")
        else:
            print("⚠️  邮件图表生成失败")
            print("请检查图表生成逻辑")
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
