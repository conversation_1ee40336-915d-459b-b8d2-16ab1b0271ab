#!/usr/bin/env python3
"""
测试b1.py最新K线分析功能
"""

import os
import pandas as pd
import numpy as np
import datetime
from b1 import KLineAnalyzer

def create_test_data_with_latest_anomaly():
    """
    创建测试数据，确保最后一根K线是异常的
    """
    print("创建测试数据（最后一根K线异常）...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    test_dir = f"test_latest/{today}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    
    symbols = ["TEST.symbol1", "TEST.symbol2", "TEST.symbol3"]
    
    for i, symbol in enumerate(symbols):
        print(f"生成 {symbol} 的数据...")
        
        # 生成30根K线数据
        n_bars = 30
        base_price = 100.0
        
        # 前29根K线正常变化
        normal_changes = np.random.normal(0, 0.2, n_bars - 1)
        
        # 最后一根K线设置为异常
        if i == 0:
            # 第一个品种：大涨（触发涨跌幅条件）
            last_change = 2.5
        elif i == 1:
            # 第二个品种：大跌（触发涨跌幅条件）
            last_change = -2.0
        else:
            # 第三个品种：中等变化（可能触发ATR或BOLL条件）
            last_change = 1.2
        
        price_changes = np.append(normal_changes, last_change)
        
        # 生成OHLC数据
        opens, highs, lows, closes, volumes = [], [], [], [], []
        current_price = base_price
        
        for j in range(n_bars):
            open_price = current_price
            change = price_changes[j]
            close_price = open_price * (1 + change / 100)
            
            if change > 0:
                high_price = close_price * (1 + np.random.uniform(0, 0.2) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.1) / 100)
            else:
                high_price = open_price * (1 + np.random.uniform(0, 0.1) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.2) / 100)
            
            volume = np.random.randint(1000, 5000)
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 计算ATR指标
        atr_tr_values = []
        atr_values = []
        
        for j in range(n_bars):
            if j == 0:
                atr_tr = highs[j] - lows[j]
                atr = atr_tr
            else:
                tr1 = highs[j] - lows[j]
                tr2 = abs(highs[j] - closes[j-1])
                tr3 = abs(lows[j] - closes[j-1])
                atr_tr = max(tr1, tr2, tr3)
                
                # 14周期移动平均
                if j < 14:
                    atr = sum(atr_tr_values + [atr_tr]) / (j + 1)
                else:
                    atr = sum(atr_tr_values[-13:] + [atr_tr]) / 14
            
            atr_tr_values.append(round(atr_tr, 4))
            atr_values.append(round(atr, 4))
        
        # 计算BOLL指标
        boll_upper, boll_middle, boll_lower = [], [], []
        
        for j in range(n_bars):
            if j < 20:
                middle = closes[j]
                std = 0.3
            else:
                period_closes = closes[max(0, j-19):j+1]
                middle = sum(period_closes) / len(period_closes)
                variance = sum((x - middle) ** 2 for x in period_closes) / len(period_closes)
                std = variance ** 0.5
            
            upper = middle + 2 * std
            lower = middle - 2 * std
            
            boll_upper.append(round(upper, 2))
            boll_middle.append(round(middle, 2))
            boll_lower.append(round(lower, 2))
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': pd.date_range('2025-01-09 09:00:00', periods=n_bars, freq='15min'),
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'atr_tr': atr_tr_values,
            'atr': atr_values,
            'boll_upper': boll_upper,
            'boll_middle': boll_middle,
            'boll_lower': boll_lower
        })
        
        # 保存到Excel文件
        file_name = f"{symbol.replace('.', '_')}_kline_indicators.xlsx"
        file_path = os.path.join(test_dir, file_name)
        df.to_excel(file_path, index=False)
        
        # 显示最后一根K线信息
        last_row = df.iloc[-1]
        last_change_pct = ((last_row['close'] - last_row['open']) / last_row['open']) * 100
        
        print(f"  文件: {file_name}")
        print(f"  最后一根K线涨跌幅: {last_change_pct:+.2f}%")
        print(f"  最后一根K线ATR: {last_row['atr']:.4f}")
        print(f"  最后一根K线实体: {abs(last_row['close'] - last_row['open']):.2f}")
        print(f"  实体/ATR比值: {abs(last_row['close'] - last_row['open']) / last_row['atr']:.2f}")
    
    print(f"\n测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_latest_kline_analysis():
    """
    测试最新K线分析功能
    """
    print("=" * 60)
    print("测试最新K线分析功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_test_data_with_latest_anomaly()
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="test_latest")
    
    # 设置较低的阈值以便测试
    analyzer.price_change_threshold = 1.0  # 1%
    analyzer.atr_multiplier = 1.5  # 1.5倍ATR
    analyzer.boll_threshold = 0.9   # 90%位置
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  分析范围: 仅最后一根K线")
    print(f"  涨跌幅阈值: {analyzer.price_change_threshold}%")
    print(f"  ATR倍数阈值: {analyzer.atr_multiplier}")
    print(f"  BOLL突破阈值: {analyzer.boll_threshold}")
    
    # 运行分析（不发送邮件）
    print(f"\n开始分析...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n测试结果:")
    print(f"  异常品种总数: {result['total_abnormal']}")
    print(f"  上涨异常: {result['up_count']} 个品种")
    print(f"  下跌异常: {result['down_count']} 个品种")
    print(f"  BOLL突破: {result['boll_breakthrough_count']} 个品种")
    print(f"  ATR超标: {result['atr_exceeded_count']} 个品种")
    print(f"  涨跌幅超标: {result['price_change_count']} 个品种")
    
    # 显示详细信息
    if result['abnormal_klines']:
        print(f"\n异常K线详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     ATR比值: {kline['body_atr_ratio']:.2f}")
            print(f"     BOLL位置: {kline['boll_position']:.2f}")
            print(f"     触发原因: {'; '.join(kline['trigger_reason'])}")
    else:
        print(f"\n未检测到异常K线（可能需要调整阈值）")
    
    # 验证是否只分析了最后一根K线
    print(f"\n验证结果:")
    if result['total_abnormal'] <= 3:  # 最多3个品种
        print(f"✓ 正确：只分析了最后一根K线（最多3个异常）")
    else:
        print(f"✗ 错误：可能分析了多根K线")
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_latest"):
        shutil.rmtree("test_latest")
        print(f"\n测试数据已清理")
    
    print(f"\n测试完成！")

if __name__ == "__main__":
    test_latest_kline_analysis()
