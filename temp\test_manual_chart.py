#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动测试图表峰值标记功能
"""

import pandas as pd
import numpy as np
from b1_macd_1 import KLineAnalyzer
import datetime

def test_chart_with_peaks():
    """
    使用现有数据测试图表峰值标记功能
    """
    print("=" * 60)
    print("手动测试图表峰值标记功能")
    print("=" * 60)
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="data", kline_period="30min")
    
    # 读取一个有数据的文件进行测试
    import os
    data_files = [f for f in os.listdir(analyzer.target_path) if f.endswith('.xlsx')]
    if not data_files:
        print("未找到数据文件")
        return False
    
    # 使用第一个文件
    test_file = data_files[0]
    file_path = os.path.join(analyzer.target_path, test_file)
    df = pd.read_excel(file_path)
    
    symbol = test_file.replace('_kline_data.xlsx', '').replace('_', '.')
    print(f"使用测试文件: {test_file}")
    print(f"品种代码: {symbol}")
    print(f"数据行数: {len(df)}")
    
    # 手动创建一个测试信号
    test_kline = {
        'symbol': symbol,
        'datetime': df.iloc[-5]['datetime'],  # 倒数第5根K线作为入场点
        'open': df.iloc[-5]['open'],
        'high': df.iloc[-5]['high'],
        'low': df.iloc[-5]['low'],
        'close': df.iloc[-5]['close'],
        'volume': df.iloc[-5]['volume'],
        'price_change': 1.5,
        'change_type': '做空',
        'macd_histogram': -2.5,
        'entry_index': -5,  # 倒数第5根K线
        'signal_type': 'MACDSHORT信号',
        'direction': 'SHORT',
        'current_peak_idx': -8,  # 倒数第8根K线作为当前峰值
        'prev_peak_idx': -25,   # 倒数第25根K线作为前一个峰值
        'trigger_reason': ['MACD顶背离信号测试'],
        'kline_data': df.copy()
    }
    
    print(f"\n测试信号创建:")
    print(f"  品种: {test_kline['symbol']}")
    print(f"  入场时间: {test_kline['datetime']}")
    print(f"  入场点索引: {test_kline['entry_index']}")
    print(f"  当前峰值索引: {test_kline['current_peak_idx']}")
    print(f"  前一峰值索引: {test_kline['prev_peak_idx']}")
    print(f"  交易方向: {test_kline['direction']}")
    
    # 生成邮件内容
    print(f"\n生成邮件内容...")
    email_content = analyzer.build_email_content([test_kline])
    
    # 保存邮件内容到HTML文件
    html_file = "test_manual_chart_preview.html"
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(email_content)
    
    print(f"邮件内容已保存到: {html_file}")
    
    # 检查邮件内容是否包含图片
    has_chart = "data:image/png;base64," in email_content
    chart_count = email_content.count("data:image/png;base64,")
    
    print(f"\n图表检查结果:")
    print(f"  包含图表: {'是' if has_chart else '否'}")
    print(f"  图表数量: {chart_count}")
    
    if has_chart:
        # 检查base64数据长度
        import re
        base64_matches = re.findall(r'data:image/png;base64,([A-Za-z0-9+/=]+)', email_content)
        if base64_matches:
            base64_length = len(base64_matches[0])
            print(f"  Base64数据长度: {base64_length} 字符")
            print(f"  预估图片大小: {base64_length * 3 // 4 // 1024:.1f} KB")
    
    return has_chart

def test_chart_generation_directly():
    """
    直接测试图表生成功能
    """
    print("\n" + "=" * 60)
    print("直接测试图表生成功能")
    print("=" * 60)
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="data", kline_period="30min")
    
    # 读取一个有数据的文件
    import os
    data_files = [f for f in os.listdir(analyzer.target_path) if f.endswith('.xlsx')]
    if not data_files:
        print("未找到数据文件")
        return False
    
    test_file = data_files[0]
    file_path = os.path.join(analyzer.target_path, test_file)
    df = pd.read_excel(file_path)
    symbol = test_file.replace('_kline_data.xlsx', '').replace('_', '.')
    
    print(f"测试数据: {symbol}")
    print(f"K线数量: {len(df)}")
    
    # 直接调用图表生成函数
    print(f"\n生成K线图表...")
    
    # 测试参数
    abnormal_index = -5  # 入场点
    peak_indices = [-8, -25]  # 峰值位置
    
    try:
        chart_base64 = analyzer.create_kline_chart(
            df, 
            symbol, 
            abnormal_index=abnormal_index,
            peak_indices=peak_indices
        )
        
        if chart_base64:
            print(f"✓ 图表生成成功")
            print(f"  Base64长度: {len(chart_base64)} 字符")
            print(f"  预估大小: {len(chart_base64) * 3 // 4 // 1024:.1f} KB")
            
            # 创建简单的HTML文件来显示图表
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>MACD峰值标记测试</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .chart {{ text-align: center; margin: 20px 0; }}
        .info {{ background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0; }}
    </style>
</head>
<body>
    <h1>MACD峰值标记功能测试</h1>
    
    <div class="info">
        <h3>测试信息</h3>
        <p><strong>品种:</strong> {symbol}</p>
        <p><strong>入场点索引:</strong> {abnormal_index}</p>
        <p><strong>峰值索引:</strong> {peak_indices}</p>
        <p><strong>图表说明:</strong></p>
        <ul>
            <li><span style="color: red;">红色箭头 + 黄色边框:</span> 入场点标记</li>
            <li><span style="color: blue;">蓝色虚线边框:</span> 峰值1标记（最近的峰值）</li>
            <li><span style="color: green;">绿色虚线边框:</span> 峰值2标记（较早的峰值）</li>
            <li><span style="color: orange;">橙色/黄色MACD柱:</span> 入场点对应的MACD柱</li>
            <li><span style="color: blue;">蓝色MACD柱:</span> 峰值1对应的MACD柱</li>
            <li><span style="color: green;">绿色MACD柱:</span> 峰值2对应的MACD柱</li>
        </ul>
    </div>
    
    <div class="chart">
        <h3>K线图 + MACD柱状图（含峰值标记）</h3>
        <img src="data:image/png;base64,{chart_base64}" alt="K线图表" style="max-width: 100%; height: auto;">
    </div>
    
    <div class="info">
        <p><strong>测试结果:</strong> 图表生成成功，包含峰值标记功能</p>
    </div>
</body>
</html>
"""
            
            with open("test_direct_chart_preview.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            
            print(f"  图表HTML已保存到: test_direct_chart_preview.html")
            return True
        else:
            print("✗ 图表生成失败")
            return False
            
    except Exception as e:
        print(f"✗ 图表生成出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("MACD峰值标记功能手动测试")
    print("=" * 60)
    
    try:
        # 测试1：通过邮件内容生成
        success1 = test_chart_with_peaks()
        
        # 测试2：直接图表生成
        success2 = test_chart_generation_directly()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if success1 and success2:
            print("🎉 MACD峰值标记功能测试成功！")
            print("✓ 邮件内容生成正常")
            print("✓ 图表生成正常")
            print("✓ 峰值标记功能正常")
            print("\n请用浏览器打开以下文件查看效果:")
            print("  - test_manual_chart_preview.html (完整邮件内容)")
            print("  - test_direct_chart_preview.html (单独图表)")
        elif success1 or success2:
            print("⚠️  部分功能测试成功")
            if success1:
                print("✓ 邮件内容生成正常")
            if success2:
                print("✓ 图表生成正常")
        else:
            print("✗ 测试失败")
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
