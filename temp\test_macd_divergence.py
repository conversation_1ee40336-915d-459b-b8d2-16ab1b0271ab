#!/usr/bin/env python3
"""
测试MACD背离分析功能
"""

import os
import pandas as pd
import numpy as np
import datetime
from b1 import KLineAnalyzer

def create_macd_divergence_test_data():
    """
    创建包含MACD背离信号的测试数据
    """
    print("创建MACD背离测试数据...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    test_dir = f"test_macd/{today}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    
    symbols = ["TEST.macd1", "TEST.macd2"]
    
    for i, symbol in enumerate(symbols):
        print(f"生成 {symbol} 的MACD背离数据...")
        
        # 生成80根30分钟K线数据（确保有足够历史数据进行MACD计算）
        n_bars = 80
        base_price = 100.0
        
        # 创建价格趋势：先下跌，然后形成背离
        price_trend = []
        
        # 前40根K线：正常波动
        for j in range(40):
            change = np.random.normal(0, 0.3)
            price_trend.append(change)
        
        # 中间20根K线：下跌趋势
        for j in range(20):
            change = np.random.normal(-0.5, 0.2)
            price_trend.append(change)
        
        # 后20根K线：价格继续下跌但MACD开始改善（形成背离）
        for j in range(20):
            if j < 10:
                change = np.random.normal(-0.3, 0.15)  # 继续下跌但幅度减小
            else:
                change = np.random.normal(-0.1, 0.1)   # 跌幅进一步减小
            price_trend.append(change)
        
        # 生成OHLC数据
        opens, highs, lows, closes, volumes = [], [], [], [], []
        current_price = base_price
        
        for j in range(n_bars):
            open_price = current_price
            change = price_trend[j]
            close_price = open_price * (1 + change / 100)
            
            if change > 0:
                high_price = close_price * (1 + np.random.uniform(0, 0.2) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.1) / 100)
            else:
                high_price = open_price * (1 + np.random.uniform(0, 0.1) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.2) / 100)
            
            volume = np.random.randint(5000, 20000)
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 创建30分钟时间序列
        start_time = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        time_list = []
        current_time = start_time
        
        for j in range(n_bars):
            time_list.append(current_time)
            current_time += datetime.timedelta(minutes=30)
            
            # 跳过非交易时间（简化处理）
            if current_time.hour == 12:  # 跳过12:00-13:30
                current_time = current_time.replace(hour=13, minute=30)
            elif current_time.hour >= 15 and current_time.hour < 21:  # 跳过15:00-21:00
                current_time = current_time.replace(hour=21, minute=0)
            elif current_time.hour >= 24:  # 跨日处理
                current_time = current_time.replace(hour=9, minute=0) + datetime.timedelta(days=1)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': time_list,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes
        })
        
        # 保存到Excel文件
        file_name = f"{symbol.replace('.', '_')}_kline_indicators.xlsx"
        file_path = os.path.join(test_dir, file_name)
        df.to_excel(file_path, index=False)
        
        # 显示信息
        print(f"  文件: {file_name}")
        print(f"  K线数量: {len(df)} 根")
        print(f"  时间范围: {df.iloc[0]['datetime']} 到 {df.iloc[-1]['datetime']}")
        print(f"  价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"  最终价格: {df.iloc[-1]['close']:.2f}")
    
    print(f"\nMACD背离测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_macd_divergence_analysis():
    """
    测试MACD背离分析功能
    """
    print("=" * 60)
    print("测试MACD背离分析功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_macd_divergence_test_data()
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="test_macd", kline_period="30min")
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  K线周期: {analyzer.kline_period}")
    print(f"  K线图历史数量: {analyzer.chart_history_bars} 根")
    print(f"  分析方法: MACD背离检测")
    
    # 运行分析
    print(f"\n开始分析...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n分析结果:")
    print(f"  信号品种总数: {result['total_abnormal']}")
    print(f"  上涨信号: {result['up_count']} 个品种")
    print(f"  下跌信号: {result['down_count']} 个品种")
    print(f"  MACD背离: {result['macd_divergence_count']} 个品种")
    
    # 生成邮件内容进行测试
    if result['abnormal_klines']:
        print(f"\n生成邮件内容...")
        email_content = analyzer.build_email_content(result['abnormal_klines'])
        
        # 保存邮件内容到HTML文件
        html_file = "test_macd_email_preview.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(email_content)
        
        print(f"邮件内容已保存到: {html_file}")
        
        # 显示MACD背离信号详情
        print(f"\nMACD背离信号详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     入场时间: {kline['datetime']}")
            print(f"     MACD柱值: {kline.get('macd_histogram', 'N/A'):.4f}")
            print(f"     入场点索引: {kline.get('entry_index', 'N/A')}")
            print(f"     信号类型: {kline.get('signal_type', 'N/A')}")
            print(f"     触发原因: {'; '.join(kline['trigger_reason'])}")
            
            # 检查K线图数据
            if 'kline_data' in kline and kline['kline_data'] is not None:
                kline_count = len(kline['kline_data'])
                has_macd = 'macd_histogram' in kline['kline_data'].columns
                print(f"     K线图数据: {kline_count} 根K线")
                print(f"     包含MACD: {'是' if has_macd else '否'}")
        
        success = True
    else:
        print("未检测到MACD背离信号")
        success = False
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_macd"):
        shutil.rmtree("test_macd")
        print(f"\n测试数据已清理")
    
    return success

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("MACD背离分析功能测试")
    print("=" * 60)
    
    try:
        # 检查依赖
        try:
            import matplotlib.pyplot as plt
            print("✓ matplotlib 可用")
        except ImportError:
            print("✗ matplotlib 不可用，请安装: pip install matplotlib")
            return
        
        # 运行测试
        success = test_macd_divergence_analysis()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if success:
            print("🎉 MACD背离分析功能测试成功！")
            print("✓ MACD背离检测正常")
            print("✓ 入场点确认正常")
            print("✓ K线图+MACD柱绘制正常")
            print("✓ 邮件内容生成正常")
            print("\n请查看 test_macd_email_preview.html 文件预览邮件效果")
        else:
            print("⚠️  测试未完成或失败")
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
