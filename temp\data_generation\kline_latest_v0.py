from tqsdk import TqApi, TqAuth
from tqsdk.ta import ATR, BOLL
import datetime
import pandas as pd
import os
import time
import schedule

## 推送实时k线数据
api = TqApi(auth=TqAuth("a_1325", "Az4719197"))

# 读取合约列表
try:
    symbols_df = pd.read_excel("generated_symbols_9_2025.xlsx")
    if not symbols_df.empty and symbols_df.shape[1] > 0:
        # 获取前5个合约代码
        symbol_list = symbols_df.iloc[:5, 0].astype(str).tolist()
        print(f"将处理以下合约: {symbol_list}")
    else:
        print("Excel文件为空或没有列。")
        symbol_list = []
except FileNotFoundError:
    print("错误: generated_symbols_9_2025.xlsx 文件未找到。")
    symbol_list = []
except Exception as e:
    print(f"读取Excel文件时发生错误: {e}")
    symbol_list = []

if not symbol_list:
    print("没有合约可供处理。程序将退出。")
    exit()

T_period = 5  # 5秒为一个K线周期
data_len = 100

# 创建保存目录
base_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
today = datetime.datetime.now().strftime('%Y%m%d')
save_dir = os.path.join(base_dir, today)

if not os.path.exists(save_dir):
    os.makedirs(save_dir)

def save_kline_data(rt_symbol):
    """保存K线数据到Excel文件"""
    try:
        # 获取K线数据
        klines = api.get_kline_serial(rt_symbol, T_period, data_length=data_len)
        
        # 计算技术指标
        atr = ATR(klines, 14)  # 14周期ATR
        boll = BOLL(klines, 20, 2)  # 20周期，2倍标准差BOLL
        
        # 创建DataFrame
        kline_data = pd.DataFrame(columns=[
            'datetime', 'open', 'high', 'low', 'close', 'volume',
            'atr_tr', 'atr',  # ATR指标
            'boll_upper', 'boll_middle', 'boll_lower'  # BOLL指标
        ])
        
        # 处理所有K线数据
        for i in range(len(klines)):
            kline = klines.iloc[i]
            current_time = datetime.datetime.fromtimestamp(kline['datetime'] / 1e9)
            
            kline_row = {
                'datetime': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                'open': kline['open'],
                'high': kline['high'],
                'low': kline['low'],
                'close': kline['close'],
                'volume': kline['volume'],
                'atr_tr': atr.tr.iloc[i],
                'atr': atr.atr.iloc[i],
                'boll_upper': boll["top"].iloc[i],
                'boll_middle': boll["mid"].iloc[i],
                'boll_lower': boll["bottom"].iloc[i]
            }
            
            kline_data = pd.concat([kline_data, pd.DataFrame([kline_row])], ignore_index=True)
        
        # 创建Excel文件名
        excel_filename = f"{rt_symbol.replace('.', '_')}_kline_data.xlsx"
        excel_path = os.path.join(save_dir, excel_filename)
        
        # 保存到Excel文件
        kline_data.to_excel(excel_path, index=False)
        
        # 打印最新K线信息
        latest_kline = klines.iloc[-1]
        latest_time = datetime.datetime.fromtimestamp(latest_kline['datetime'] / 1e9)
        
        print(f"\n合约 {rt_symbol} 数据已更新 - 时间: {latest_time.strftime('%Y-%m-%d %H:%M:%S')}")
        # print(f"开盘价: {latest_kline['open']}")
        # print(f"最高价: {latest_kline['high']}")
        # print(f"最低价: {latest_kline['low']}")
        # print(f"收盘价: {latest_kline['close']}")
        # print(f"成交量: {latest_kline['volume']}")
        # print(f"ATR(TR): {atr.tr.iloc[-1]:.2f}")
        # print(f"ATR: {atr.atr.iloc[-1]:.2f}")
        # print(f"BOLL上轨: {boll['top'].iloc[-1]:.2f}")
        # print(f"BOLL中轨: {boll['mid'].iloc[-1]:.2f}")
        # print(f"BOLL下轨: {boll['bottom'].iloc[-1]:.2f}")
        # print(f"数据已保存到: {excel_path}")
        
    except Exception as e:
        print(f"处理合约 {rt_symbol} 时发生错误: {e}")

def update_all_contracts():
    """更新所有合约的数据"""
    print("\n开始更新所有合约数据...")
    for rt_symbol in symbol_list:
        save_kline_data(rt_symbol)

# 设置定时任务
schedule.every(T_period).seconds.do(update_all_contracts)

print(f"\n开始定时更新数据，更新间隔: {T_period}秒")

try:
    while True:
        schedule.run_pending()
        time.sleep(1)
except KeyboardInterrupt:
    print("\n程序被用户中断")
finally:
    print("\n程序结束，所有数据已保存到Excel文件。")
    api.close()