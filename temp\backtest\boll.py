#!/usr/bin/env python
#  -*- coding: utf-8 -*-
__author__ = "Ringo"

'''
Aberration策略 (难度：初级) 布林带回测示例
参考: https://www.shinnytech.com/blog/aberration/
注: 该示例策略仅用于功能示范, 实盘时请根据自己的策略/经验进行修改
'''

from tqsdk import TqApi, TqAuth, TargetPosTask
from tqsdk.ta import BOLL
from tqsdk.ta import MA
from datetime import date
from tqsdk import TqSim, TqBacktest
import mplfinance as mpf
import pandas as pd
import numpy as np
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

# 设置合约代码
SYMBOL = "DCE.m2509"
acc=TqSim()
api = TqApi(account=acc, backtest=TqBacktest(start_dt=date(2025, 5, 1), end_dt=date(2025, 5, 16)),  auth=TqAuth("a_1325", "Az4719197"),web_gui=True)
quote = api.get_quote(SYMBOL)
#ticks = api.get_tick_serial(SYMBOL, 60 * 60 * 2)
klines = api.get_kline_serial(SYMBOL, 60 * 60 * 2)
position = api.get_position(SYMBOL)
target_pos = TargetPosTask(api, SYMBOL)

# 使用BOLL指标计算中轨、上轨和下轨，其中26为周期N  ，2为参数p
def boll_line(klines):
    boll = BOLL(klines, 26, 2)
    midline = boll["mid"].iloc[-1]
    topline = boll["top"].iloc[-1]
    bottomline = boll["bottom"].iloc[-1]
    print("策略运行，中轨：%.2f，上轨为:%.2f，下轨为:%.2f" % (midline, topline, bottomline))
    return midline, topline, bottomline

# 用于记录交易信号
trade_signals = []

try:
    midline, topline, bottomline = boll_line(klines)
    
    while True:
        api.wait_update()
        # 每次生成新的K线时重新计算BOLL指标
        if api.is_changing(klines.iloc[-1], "datetime"):
            midline, topline, bottomline = boll_line(klines)
            klines["midline"] = midline  # 在主图中画一根默认颜色（红色）的 ma 指标线
            klines["topline"] = topline  # 在主图中画一根默认颜色（红色）的 ma 指标线
            klines["bottomline"] = bottomline  # 在主图中画一根默认颜色（红色）的 ma 指标线

        # 每次最新价发生变化时进行判断
        if api.is_changing(quote, "last_price"):
            # 判断开仓条件
            if position.pos_long == 0 and position.pos_short == 0:
                # 如果最新价大于上轨，K线上穿上轨，开多仓
                if quote.last_price > topline:
                    print("K线上穿上轨，开多仓")
                    target_pos.set_target_volume(20)
                    trade_signals.append({"datetime": klines.iloc[-1]["datetime"], "price": quote.last_price, "type": "buy"})
                # 如果最新价小于轨，K线下穿下轨，开空仓
                elif quote.last_price < bottomline:
                    print("K线下穿下轨，开空仓")
                    target_pos.set_target_volume(-20)
                    trade_signals.append({"datetime": klines.iloc[-1]["datetime"], "price": quote.last_price, "type": "sell"})
                #else:
                    #print("当前最新价%.2f,未穿上轨或下轨，不开仓" % quote.last_price)

            # 在多头情况下，空仓条件
            elif position.pos_long > 0:
                # 如果最新价低于中线，多头清仓离场
                if quote.last_price < midline:
                    print("最新价低于中线，多头清仓离场")
                    target_pos.set_target_volume(0)
                    trade_signals.append({"datetime": klines.iloc[-1]["datetime"], "price": quote.last_price, "type": "close_long"})
                #else:
                    #print("当前多仓，未穿越中线，仓位无变化")

            # 在空头情况下，空仓条件
            elif position.pos_short > 0:
                # 如果最新价高于中线，空头清仓离场
                if quote.last_price > midline:
                    print("最新价高于中线，空头清仓离场")
                    target_pos.set_target_volume(0)
                    trade_signals.append({"datetime": klines.iloc[-1]["datetime"], "price": quote.last_price, "type": "close_short"})
                #else:
                    #print("当前空仓，未穿越中线，仓位无变化")

finally:
    # 创建Excel工作簿
    wb = openpyxl.Workbook()
    
    # 创建交易记录表
    trade_sheet = wb.active
    trade_sheet.title = "交易记录"
    
    # 设置表头
    headers = ["交易时间", "合约", "方向", "开平", "成交价", "成交手数", "手续费", "成交额"]
    for col, header in enumerate(headers, 1):
        cell = trade_sheet.cell(row=1, column=col)
        cell.value = header
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")
    
    # 写入交易记录
    for row, trade in enumerate(acc.trade_log, 2):
        trade_sheet.cell(row=row, column=1, value=trade["time"])
        trade_sheet.cell(row=row, column=2, value=trade["symbol"])
        trade_sheet.cell(row=row, column=3, value=trade["direction"])
        trade_sheet.cell(row=row, column=4, value=trade["offset"])
        trade_sheet.cell(row=row, column=5, value=trade["price"])
        trade_sheet.cell(row=row, column=6, value=trade["volume"])
        trade_sheet.cell(row=row, column=7, value=trade["fee"])
        trade_sheet.cell(row=row, column=8, value=trade["amount"])
    
    # 创建统计信息表
    stat_sheet = wb.create_sheet(title="统计信息")
    
    # 写入统计信息
    stats = acc.tqsdk_stat
    row = 1
    for key, value in stats.items():
        stat_sheet.cell(row=row, column=1, value=key)
        stat_sheet.cell(row=row, column=2, value=value)
        row += 1
    
    # 调整列宽
    for sheet in [trade_sheet, stat_sheet]:
        for column in sheet.columns:
            max_length = 0
            column = [cell for cell in column]
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            sheet.column_dimensions[get_column_letter(column[0].column)].width = adjusted_width
    
    # 保存Excel文件
    wb.save("boll_strategy_backtest.xlsx")
    
    print("回测结果已保存到 boll_strategy_backtest.xlsx")
    print(acc.trade_log)
    print(acc.tqsdk_stat)
    
    # 由于需要在浏览器中查看绘图结果，因此程序不能退出
    while True:
        api.wait_update()
