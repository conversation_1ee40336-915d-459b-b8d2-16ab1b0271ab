from tqsdk import TqApi, TqAuth
from tqsdk.ta import BOLL, ATR, EMA
import datetime
import pandas as pd
import os
import openpyxl # For Excel operations
# 读取合约列表，保存历史K线以及BOLL指标

# id: 1234 (k线序列号)
# datetime: 1501080715000000000 (K线起点时间(按北京时间)，自unix epoch(1970-01-01 00:00:00 GMT)以来的纳秒数)
# open: 51450.0 (K线起始时刻的最新价)
# high: 51450.0 (K线时间范围内的最高价)
# low: 51450.0 (K线时间范围内的最低价)
# close: 51450.0 (K线结束时刻的最新价)
# volume: 11 (K线时间范围内的成交量)
# open_oi: 27354 (K线起始时刻的持仓量)
# close_oi: 27355 (K线结束时刻的持仓量)

# 使用BOLL指标计算中轨、上轨和下轨，其中26为周期N  ，2为参数p
def boll_line(klines):
    boll = BOLL(klines, 26, 2) # klines is a TQSDK DataFrame-like object
    midline = boll["mid"].iloc[-1]
    topline = boll["top"].iloc[-1]
    bottomline = boll["bottom"].iloc[-1]
    # print("布林带最新值：中轨：%.2f，上轨为:%.2f，下轨为:%.2f" % (midline, topline, bottomline))
    return midline, topline, bottomline
    
def save_kline_data(api, symbol_list, period, data_length=100):
    """
    获取并保存K线数据到指定目录
    
    参数:
    api: TqApi实例
    symbol_list: 合约代码列表
    period: K线周期（秒），默认30分钟
    data_length: 获取的K线数量，默认100根
    
    返回:
    bool: 是否成功保存数据
    """
    try:
        # 设置保存路径
        base_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
        today = datetime.datetime.now().strftime('%Y%m%d')
        save_dir = os.path.join(base_dir, today)
        
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
        print(f"数据将保存到目录: {save_dir}")
        
        for rt_symbol in symbol_list:
            print(f"\n正在处理合约: {rt_symbol}...")
            current_symbol_df = pd.DataFrame()
            
            try:
                klines_tq = api.get_kline_serial(rt_symbol, period, data_length=data_length)
                
                if klines_tq is not None and len(klines_tq) > 0:
                    print(f"成功获取 {len(klines_tq)} 条K线数据。")
                    
                    # 计算技术指标
                    # BOLL指标
                    boll = BOLL(klines_tq, 26, 2)
                    mid = boll["mid"].iloc[-1]
                    top = boll["top"].iloc[-1]
                    bottom = boll["bottom"].iloc[-1]
                    print(f"布林带最新值: 中轨={mid:.2f}, 上轨={top:.2f}, 下轨={bottom:.2f}")
                    
                    # ATR指标
                    atr = ATR(klines_tq, 14)  # 14周期ATR
                    atr_value = atr.atr.iloc[-1]
                    atr_tr = atr.tr.iloc[-1]
                    print(f"ATR指标最新值: ATR={atr_value:.2f}, TR={atr_tr:.2f}")
                    
                    # EMA指标
                    ema = EMA(klines_tq, 20)  # 20周期EMA
                    ema_value = ema.ema.iloc[-1]
                    print(f"EMA指标最新值: EMA20={ema_value:.2f}")
                    
                    # 创建DataFrame
                    klines_df_for_symbol = pd.DataFrame({
                        'datetime_nano': klines_tq['datetime'],
                        'open': klines_tq['open'],
                        'high': klines_tq['high'],
                        'low': klines_tq['low'],
                        'close': klines_tq['close'],
                        'volume': klines_tq['volume']
                    })
                    
                    # 转换时间格式
                    klines_df_for_symbol['datetime'] = klines_df_for_symbol['datetime_nano'].apply(
                        lambda x: datetime.datetime.fromtimestamp(x / 1e9).strftime('%Y-%m-%d %H:%M:%S')
                    )
                    
                    # 添加技术指标
                    klines_df_for_symbol['boll_midline'] = boll["mid"]
                    klines_df_for_symbol['boll_topline'] = boll["top"]
                    klines_df_for_symbol['boll_bottomline'] = boll["bottom"]
                    klines_df_for_symbol['atr'] = atr.atr
                    klines_df_for_symbol['atr_tr'] = atr.tr
                    klines_df_for_symbol['ema_20'] = ema.ema
                    
                    # 选择需要的列
                    current_symbol_df = klines_df_for_symbol[[
                        'datetime', 'open', 'high', 'low', 'close', 'volume',
                        'boll_midline', 'boll_topline', 'boll_bottomline',
                        'atr', 'atr_tr', 'ema_20'
                    ]]
                    
                    if not current_symbol_df.empty:
                        # 创建安全的文件名
                        safe_symbol_name = rt_symbol.replace(".", "_").replace("/", "_")
                        excel_file_name = f"{safe_symbol_name}_kline_indicators.xlsx"
                        excel_path = os.path.join(save_dir, excel_file_name)
                        
                        # 保存到Excel
                        current_symbol_df.to_excel(excel_path, index=False, sheet_name="Klines_Indicators")
                        print(f"合约 {rt_symbol} 的数据已成功保存到 {excel_path}")
                    else:
                        print(f"合约 {rt_symbol} 没有生成可保存的数据。")
                else:
                    print(f"未能获取合约 {rt_symbol} 的K线数据。")
            except Exception as e_symbol:
                print(f"处理合约 {rt_symbol} 时发生错误: {e_symbol}")
                continue
                
        return True
        
    except Exception as e:
        print(f"保存K线数据时发生错误: {e}")
        return False

def get_symbol_list(excel_file="generated_symbols_9_2025.xlsx", max_symbols=5):
    """
    从Excel文件读取合约列表
    
    参数:
    excel_file: Excel文件名
    max_symbols: 最大合约数量
    
    返回:
    list: 合约代码列表
    """
    try:
        print("正在读取合约列表文件...")
        symbols_df = pd.read_excel(excel_file)
        if not symbols_df.empty and symbols_df.shape[1] > 0:
            symbol_list = symbols_df.iloc[:, 0].dropna().astype(str).tolist()[:max_symbols]
            if symbol_list:
                print(f"将处理以下前{max_symbols}个合约: {symbol_list}")
                return symbol_list
            else:
                print("未能从Excel文件中读取到合约代码，或合约代码数量不足。")
        else:
            print("Excel文件为空或没有列。")
    except FileNotFoundError:
        print(f"错误: {excel_file} 文件未找到。")
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")
    
    return []

def main(api,T_period):
    """
    主函数
    """
    try:
        # 创建API实例
        # api = TqApi(auth=TqAuth("a_1325", "Az4719197"))
        
        # 获取合约列表
        symbol_list = get_symbol_list()
        
        if not symbol_list:
            print("没有合约可供处理。程序将退出。")
            return
            
        # 保存K线数据
        success = save_kline_data(api, symbol_list, period=T_period, data_length=100)
        
        if success:
            print("\n所有数据已成功保存。")
        else:
            print("\n保存数据时发生错误。")
            
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
    #finally:
        #print("\n关闭API连接...")
        #api.close()
        #print("API连接已关闭。")

if __name__ == "__main__":
    api = TqApi(auth=TqAuth("a_1325", "Az4719197"))
    T_period_1 = 60*10
    T_period_2 = 60*10
    klines = api.get_kline_serial("SHFE.cu2509", T_period_1)        # 获取SHFE.cu1812合约的10秒K线
    main(api,T_period_2)
    while api.wait_update():
        if api.is_changing(klines.iloc[-1], "datetime"):    # 判定最后一根K线的时间是否有变化
            print("K线更新")                          # 当最后一根K线的时间有变(新K线生成)时才会执行到这里
            main(api,T_period_2)