#!/usr/bin/env python3
"""
测试包含K线图的邮件功能
"""

import os
import pandas as pd
import numpy as np
import datetime
from b1 import KLineAnalyzer

def create_test_data_with_charts():
    """
    创建测试数据用于邮件图表功能测试
    """
    print("创建测试数据（用于图表邮件测试）...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    test_dir = f"test_email_charts/{today}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    
    symbols = ["TEST.chart1", "TEST.chart2"]
    
    for i, symbol in enumerate(symbols):
        print(f"生成 {symbol} 的数据...")
        
        # 生成30根K线数据
        n_bars = 30
        base_price = 100.0
        
        # 前28根K线正常变化
        normal_changes = np.random.normal(0, 0.3, n_bars - 2)
        
        # 倒数第二根K线设置为异常
        if i == 0:
            # 第一个品种：ATR超标
            second_last_change = 1.0
        else:
            # 第二个品种：BOLL突破
            second_last_change = 0.5
        
        # 最后一根K线正常
        last_change = 0.1
        
        price_changes = np.append(normal_changes, [second_last_change, last_change])
        
        # 生成OHLC数据
        opens, highs, lows, closes, volumes = [], [], [], [], []
        current_price = base_price
        
        for j in range(n_bars):
            open_price = current_price
            change = price_changes[j]
            close_price = open_price * (1 + change / 100)
            
            if change > 0:
                high_price = close_price * (1 + np.random.uniform(0, 0.2) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.1) / 100)
            else:
                high_price = open_price * (1 + np.random.uniform(0, 0.1) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.2) / 100)
            
            volume = np.random.randint(1000, 5000)
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 计算ATR指标
        atr_tr_values = []
        atr_values = []
        
        for j in range(n_bars):
            if j == 0:
                atr_tr = highs[j] - lows[j]
                atr = atr_tr
            else:
                tr1 = highs[j] - lows[j]
                tr2 = abs(highs[j] - closes[j-1])
                tr3 = abs(lows[j] - closes[j-1])
                atr_tr = max(tr1, tr2, tr3)
                
                if j < 14:
                    atr = sum(atr_tr_values + [atr_tr]) / (j + 1)
                else:
                    atr = sum(atr_tr_values[-13:] + [atr_tr]) / 14
            
            atr_tr_values.append(round(atr_tr, 4))
            atr_values.append(round(atr, 4))
        
        # 人为调整倒数第二根K线的ATR
        if i == 0:  # 第一个品种ATR超标
            target_atr = abs(closes[-2] - opens[-2]) / 2.5  # 使实体/ATR比值为2.5
            atr_values[-2] = round(target_atr, 4)
        
        # 计算BOLL指标
        boll_upper, boll_middle, boll_lower = [], [], []
        
        for j in range(n_bars):
            if j < 20:
                middle = closes[j]
                std = 0.5
            else:
                period_closes = closes[max(0, j-19):j+1]
                middle = sum(period_closes) / len(period_closes)
                variance = sum((x - middle) ** 2 for x in period_closes) / len(period_closes)
                std = variance ** 0.5
            
            upper = middle + 2 * std
            lower = middle - 2 * std
            
            boll_upper.append(round(upper, 2))
            boll_middle.append(round(middle, 2))
            boll_lower.append(round(lower, 2))
        
        # 人为调整倒数第二根K线的BOLL位置
        if i == 1:  # 第二个品种BOLL突破
            boll_range = boll_upper[-2] - boll_lower[-2]
            target_close = boll_lower[-2] + boll_range * 0.98  # 接近上轨
            closes[-2] = round(target_close, 2)
        
        # 创建DataFrame，包含时间信息
        start_time = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        time_list = [start_time + datetime.timedelta(minutes=15*j) for j in range(n_bars)]
        
        df = pd.DataFrame({
            'datetime': time_list,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'atr_tr': atr_tr_values,
            'atr': atr_values,
            'boll_upper': boll_upper,
            'boll_middle': boll_middle,
            'boll_lower': boll_lower
        })
        
        # 保存到Excel文件
        file_name = f"{symbol.replace('.', '_')}_kline_indicators.xlsx"
        file_path = os.path.join(test_dir, file_name)
        df.to_excel(file_path, index=False)
        
        # 显示倒数第二根K线信息
        second_last_row = df.iloc[-2]
        actual_change = ((second_last_row['close'] - second_last_row['open']) / second_last_row['open']) * 100
        actual_atr_ratio = abs(second_last_row['close'] - second_last_row['open']) / second_last_row['atr']
        actual_boll_pos = (second_last_row['close'] - second_last_row['boll_lower']) / (second_last_row['boll_upper'] - second_last_row['boll_lower'])
        
        print(f"  文件: {file_name}")
        print(f"  倒数第二根K线时间: {second_last_row['datetime']}")
        print(f"  实际涨跌幅: {actual_change:+.2f}%")
        print(f"  实际ATR比值: {actual_atr_ratio:.2f}")
        print(f"  实际BOLL位置: {actual_boll_pos:.2f}")
    
    print(f"\n测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_email_with_charts():
    """
    测试包含图表的邮件功能
    """
    print("=" * 60)
    print("测试包含K线图的邮件功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_test_data_with_charts()
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="test_email_charts")
    
    # 设置阈值
    analyzer.atr_multiplier = 2.0
    analyzer.boll_threshold = 0.95
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  ATR倍数阈值: {analyzer.atr_multiplier}")
    print(f"  BOLL突破阈值: {analyzer.boll_threshold}")
    
    # 运行分析（不发送邮件，但生成邮件内容）
    print(f"\n开始分析...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n分析结果:")
    print(f"  异常品种总数: {result['total_abnormal']}")
    print(f"  ATR超标: {result['atr_exceeded_count']} 个品种")
    print(f"  BOLL突破: {result['boll_breakthrough_count']} 个品种")
    
    # 生成邮件内容进行测试
    if result['abnormal_klines']:
        print(f"\n生成邮件内容...")
        email_content = analyzer.build_email_content(result['abnormal_klines'])
        
        # 保存邮件内容到HTML文件进行预览
        html_file = "test_email_preview.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(email_content)
        
        print(f"邮件内容已保存到: {html_file}")
        print("可以用浏览器打开该文件预览邮件效果")
        
        # 显示异常K线详情
        print(f"\n异常K线详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     开盘时间: {kline['datetime']}")
            print(f"     ATR比值: {kline['body_atr_ratio']:.2f}")
            print(f"     BOLL位置: {kline['boll_position']:.2f}")
            print(f"     触发原因: {'; '.join(kline['trigger_reason'])}")
            print(f"     包含K线图: {'是' if 'kline_data' in kline else '否'}")
        
        success = True
    else:
        print("未检测到异常K线，无法测试邮件功能")
        success = False
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_email_charts"):
        shutil.rmtree("test_email_charts")
        print(f"\n测试数据已清理")
    
    return success

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("K线图邮件功能测试")
    print("=" * 60)
    
    try:
        # 检查依赖
        try:
            import matplotlib.pyplot as plt
            print("✓ matplotlib 可用")
        except ImportError:
            print("✗ matplotlib 不可用，请安装: pip install matplotlib")
            return
        
        # 运行测试
        success = test_email_with_charts()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if success:
            print("🎉 邮件图表功能测试成功！")
            print("✓ K线图生成正常")
            print("✓ 邮件内容包含图表")
            print("✓ 开盘时间正确显示")
            print("✓ 异常K线标记清晰")
            print("\n请查看 test_email_preview.html 文件预览邮件效果")
        else:
            print("⚠️  测试未完成或失败")
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
