#!/usr/bin/env python3
"""
测试b2.py调度器功能
"""

import os
import sys
import time
import subprocess
import datetime

def create_mock_scripts():
    """
    创建模拟的a.py和b1.py脚本用于测试
    """
    print("创建模拟脚本...")
    
    # 创建模拟的a.py脚本
    mock_a_content = '''#!/usr/bin/env python3
import time
import datetime
import os

print("=" * 50)
print("模拟数据获取脚本 (a.py)")
print("=" * 50)

print("开始获取K线数据...")
time.sleep(2)  # 模拟数据获取过程

# 创建模拟数据目录和文件
today = datetime.datetime.now().strftime('%Y%m%d')
data_dir = f"data/{today}"
os.makedirs(data_dir, exist_ok=True)

# 创建模拟数据文件
test_file = os.path.join(data_dir, "test_data.txt")
with open(test_file, "w") as f:
    f.write(f"模拟数据文件，创建时间: {datetime.datetime.now()}")

print(f"数据已保存到: {data_dir}")
print("数据获取完成！")
'''
    
    with open("a.py", "w", encoding="utf-8") as f:
        f.write(mock_a_content)
    
    # 创建模拟的b1.py脚本
    mock_b1_content = '''#!/usr/bin/env python3
import time
import datetime
import os

print("=" * 50)
print("模拟K线分析脚本 (b1.py)")
print("=" * 50)

# 检查数据文件是否存在
today = datetime.datetime.now().strftime('%Y%m%d')
data_dir = f"data/{today}"
test_file = os.path.join(data_dir, "test_data.txt")

if os.path.exists(test_file):
    print(f"找到数据文件: {test_file}")
    with open(test_file, "r") as f:
        content = f.read()
    print(f"数据内容: {content}")
else:
    print("警告: 未找到数据文件！")

print("开始分析倒数第二根K线...")
time.sleep(1)  # 模拟分析过程

print("分析完成，未发现异常K线")
print("分析脚本执行完成！")
'''
    
    with open("b1.py", "w", encoding="utf-8") as f:
        f.write(mock_b1_content)
    
    print("模拟脚本创建完成:")
    print("  - a.py (模拟数据获取)")
    print("  - b1.py (模拟K线分析)")

def test_scheduler_info():
    """
    测试调度器信息显示
    """
    print("\n" + "=" * 60)
    print("测试调度器信息显示")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, "b2.py", "info"], 
                              capture_output=True, text=True, timeout=10)
        print("调度器信息:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("测试超时")
        return False
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_single_scripts():
    """
    测试单独运行脚本
    """
    print("\n" + "=" * 60)
    print("测试单独运行脚本")
    print("=" * 60)
    
    # 测试数据获取脚本
    print("1. 测试数据获取脚本...")
    try:
        result = subprocess.run([sys.executable, "b2.py", "test-data"], 
                              capture_output=True, text=True, timeout=30)
        print("数据获取脚本测试结果:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        data_success = result.returncode == 0
    except Exception as e:
        print(f"数据获取脚本测试失败: {e}")
        data_success = False
    
    time.sleep(2)
    
    # 测试分析脚本
    print("\n2. 测试分析脚本...")
    try:
        result = subprocess.run([sys.executable, "b2.py", "test-analysis"], 
                              capture_output=True, text=True, timeout=30)
        print("分析脚本测试结果:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        analysis_success = result.returncode == 0
    except Exception as e:
        print(f"分析脚本测试失败: {e}")
        analysis_success = False
    
    return data_success and analysis_success

def test_task_sequence():
    """
    测试任务序列
    """
    print("\n" + "=" * 60)
    print("测试任务序列 (a.py -> b1.py)")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, "b2.py", "test"], 
                              capture_output=True, text=True, timeout=60)
        print("任务序列测试结果:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        # 检查是否按顺序执行
        output = result.stdout
        if "第1步：运行数据获取脚本" in output and "第2步：运行K线分析脚本" in output:
            print("✓ 任务按正确顺序执行")
            return result.returncode == 0
        else:
            print("✗ 任务执行顺序不正确")
            return False
            
    except subprocess.TimeoutExpired:
        print("测试超时")
        return False
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_status():
    """
    测试状态显示
    """
    print("\n" + "=" * 60)
    print("测试状态显示")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, "b2.py", "status"], 
                              capture_output=True, text=True, timeout=10)
        print("状态信息:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def cleanup():
    """
    清理测试文件
    """
    print("\n清理测试文件...")
    
    files_to_remove = ["a.py", "b1.py"]
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"删除: {file}")
    
    # 清理数据目录
    if os.path.exists("data"):
        import shutil
        shutil.rmtree("data")
        print("删除: data目录")
    
    print("清理完成")

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("b2.py 调度器功能测试")
    print("=" * 60)
    
    # 检查b2.py是否存在
    if not os.path.exists("b2.py"):
        print("错误: b2.py 文件不存在")
        return
    
    try:
        # 创建模拟脚本
        create_mock_scripts()
        
        # 运行测试
        tests = [
            ("调度器信息显示", test_scheduler_info),
            ("单独脚本运行", test_single_scripts),
            ("任务序列执行", test_task_sequence),
            ("状态显示", test_status),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n正在运行测试: {test_name}")
            success = test_func()
            results.append((test_name, success))
            print(f"测试结果: {'✓ 通过' if success else '✗ 失败'}")
        
        # 显示测试总结
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        passed = 0
        for test_name, success in results:
            status = "✓ 通过" if success else "✗ 失败"
            print(f"  {test_name}: {status}")
            if success:
                passed += 1
        
        print(f"\n总计: {passed}/{len(results)} 个测试通过")
        
        if passed == len(results):
            print("🎉 所有测试通过！b2.py调度器功能正常")
        else:
            print("⚠️  部分测试失败，请检查b2.py配置")
    
    finally:
        # 清理测试文件
        cleanup()

if __name__ == "__main__":
    main()
