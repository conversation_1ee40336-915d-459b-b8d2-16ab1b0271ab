#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的邮件发送方案，支持多种图片显示方式
"""

import os
import base64
import tempfile
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.image import MIMEImage

def create_email_with_embedded_images(subject, html_content, chart_data_list):
    """
    创建包含嵌入图片的邮件
    
    参数:
    subject: 邮件主题
    html_content: HTML内容（包含内联base64图片）
    chart_data_list: 图片数据列表 [{'symbol': 'XXX', 'chart_base64': 'xxx'}, ...]
    
    返回:
    MIMEMultipart: 邮件对象
    """
    # 创建邮件对象
    msg = MIMEMultipart('related')
    msg['Subject'] = subject
    msg['From'] = "<EMAIL>"
    msg['To'] = "<EMAIL>"
    
    # 创建HTML内容（使用cid引用图片）
    html_with_cid = html_content
    
    # 替换base64图片为cid引用
    for i, chart_data in enumerate(chart_data_list):
        symbol = chart_data['symbol']
        chart_base64 = chart_data['chart_base64']
        
        # 生成唯一的cid
        cid = f"chart_{i}_{symbol.replace('.', '_')}"
        
        # 替换HTML中的base64图片为cid引用
        old_img_tag = f'<img src="data:image/png;base64,{chart_base64}"'
        new_img_tag = f'<img src="cid:{cid}"'
        html_with_cid = html_with_cid.replace(old_img_tag, new_img_tag)
        
        # 添加图片附件
        try:
            img_data = base64.b64decode(chart_base64)
            img_attachment = MIMEImage(img_data)
            img_attachment.add_header('Content-ID', f'<{cid}>')
            img_attachment.add_header('Content-Disposition', 'inline', filename=f'{symbol}_chart.png')
            msg.attach(img_attachment)
        except Exception as e:
            print(f"添加图片附件失败 {symbol}: {e}")
    
    # 添加HTML内容
    html_part = MIMEText(html_with_cid, 'html', 'utf-8')
    msg.attach(html_part)
    
    return msg

def create_email_with_attachments(subject, html_content_no_images, chart_data_list):
    """
    创建包含图片附件的邮件（不使用内联图片）
    
    参数:
    subject: 邮件主题
    html_content_no_images: 不包含图片的HTML内容
    chart_data_list: 图片数据列表
    
    返回:
    MIMEMultipart: 邮件对象
    """
    # 创建邮件对象
    msg = MIMEMultipart()
    msg['Subject'] = subject + " [图片以附件形式发送]"
    msg['From'] = "<EMAIL>"
    msg['To'] = "<EMAIL>"
    
    # 修改HTML内容，移除图片，添加附件说明
    html_modified = html_content_no_images.replace(
        '<div class="chart-container">',
        '<div class="chart-container"><p style="color: #666; font-style: italic;">📎 K线图表已作为附件发送，请查看邮件附件。</p>'
    )
    
    # 添加HTML内容
    html_part = MIMEText(html_modified, 'html', 'utf-8')
    msg.attach(html_part)
    
    # 添加图片附件
    for i, chart_data in enumerate(chart_data_list):
        symbol = chart_data['symbol']
        chart_base64 = chart_data['chart_base64']
        
        try:
            img_data = base64.b64decode(chart_base64)
            img_attachment = MIMEImage(img_data)
            img_attachment.add_header(
                'Content-Disposition', 
                'attachment', 
                filename=f'{symbol}_MACD_chart_{i+1}.png'
            )
            msg.attach(img_attachment)
        except Exception as e:
            print(f"添加图片附件失败 {symbol}: {e}")
    
    return msg

def create_simple_text_email(subject, abnormal_klines):
    """
    创建纯文本邮件（备用方案）
    
    参数:
    subject: 邮件主题
    abnormal_klines: 异常K线列表
    
    返回:
    MIMEText: 邮件对象
    """
    import datetime
    
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    text_content = f"""
MACD背离信号提醒

分析时间: {current_time}
检测到 {len(abnormal_klines)} 个品种的MACD背离信号

详细信息:
"""
    
    for i, kline in enumerate(abnormal_klines, 1):
        direction = kline.get('direction', 'UNKNOWN')
        direction_text = '做多' if direction == 'LONG' else '做空' if direction == 'SHORT' else '未知'
        
        text_content += f"""
{i}. {kline['symbol']} - {direction_text} {kline['price_change']:+.2f}%
   入场时间: {kline['datetime']}
   开盘价: {kline['open']:.2f}
   收盘价: {kline['close']:.2f}
   最高价: {kline['high']:.2f}
   最低价: {kline['low']:.2f}
   MACD柱值: {kline.get('macd_histogram', 'N/A'):.4f}
   信号类型: {kline.get('signal_type', 'MACD背离')}
   触发原因: {'; '.join(kline['trigger_reason'])}

"""
    
    text_content += """
注意: 由于邮件客户端限制，K线图表无法显示。
建议使用支持HTML邮件的客户端查看完整报告。

分析参数:
- K线周期: 30分钟
- MACD参数: 快线12，慢线26，信号线9
- 背离检测: MACD波峰变化 + 价格反向变化
- 入场条件: MACD柱绝对值减小
"""
    
    msg = MIMEText(text_content, 'plain', 'utf-8')
    msg['Subject'] = subject + " [纯文本版本]"
    msg['From'] = "<EMAIL>"
    msg['To'] = "<EMAIL>"
    
    return msg

def build_email_content_without_images(abnormal_klines):
    """
    构建不包含内联图片的邮件内容
    """
    import datetime
    
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    html_content = f"""
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
            .header {{ background-color: #f0f0f0; padding: 15px; text-align: center; margin-bottom: 20px; }}
            .content {{ padding: 0; }}
            .highlight-up {{ color: green; font-weight: bold; }}
            .highlight-down {{ color: red; font-weight: bold; }}
            .summary {{ background-color: #e8f4fd; padding: 15px; margin-bottom: 20px; border-radius: 5px; }}
            .symbol-section {{ margin-bottom: 40px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden; }}
            .symbol-header {{ background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }}
            .symbol-content {{ padding: 20px; }}
            .kline-info {{ display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px; }}
            .info-item {{ flex: 1; min-width: 200px; }}
            .info-label {{ font-weight: bold; color: #666; }}
            .info-value {{ margin-top: 5px; }}
            .chart-container {{ text-align: center; margin: 20px 0; background: #f9f9f9; padding: 20px; border-radius: 5px; }}
            .trigger-reasons {{ background-color: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h2>MACD背离信号提醒（30分钟K线）</h2>
        </div>
        <div class="content">
            <div class="summary">
                <p><strong>检测到 {len(abnormal_klines)} 个品种的MACD背离信号！</strong></p>
                <p><strong>分析时间：</strong>{current_time}</p>
                <p><strong>分析方法：</strong>MACD背离检测（底背离+顶背离） + 入场点确认</p>
                <p><strong>做多信号：</strong>MACD柱<0且绝对值减小（底背离）</p>
                <p><strong>做空信号：</strong>MACD柱>0且绝对值减小（顶背离）</p>
            </div>
    """
    
    # 为每个异常品种创建展示区域（不包含图片）
    for i, kline in enumerate(abnormal_klines, 1):
        direction = kline.get('direction', 'UNKNOWN')
        direction_text = '做多' if direction == 'LONG' else '做空' if direction == 'SHORT' else kline['change_type']
        highlight_class = "highlight-up" if direction == 'LONG' else "highlight-down"
        
        # 格式化时间
        try:
            if isinstance(kline['datetime'], str):
                datetime_obj = pd.to_datetime(kline['datetime'])
            else:
                datetime_obj = kline['datetime']
            datetime_str = datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
        except:
            datetime_str = str(kline['datetime'])
        
        trigger_reasons = "; ".join(kline['trigger_reason'])
        
        html_content += f"""
            <div class="symbol-section">
                <div class="symbol-header">
                    <h3>#{i} {kline['symbol']} - <span class="{highlight_class}">{direction_text} {kline['price_change']:+.2f}%</span></h3>
                </div>
                <div class="symbol-content">
                    <div class="kline-info">
                        <div class="info-item">
                            <div class="info-label">入场时间</div>
                            <div class="info-value">{datetime_str}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">开盘价</div>
                            <div class="info-value">{kline['open']:.2f}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">收盘价</div>
                            <div class="info-value">{kline['close']:.2f}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最高价</div>
                            <div class="info-value">{kline['high']:.2f}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最低价</div>
                            <div class="info-value">{kline['low']:.2f}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">MACD柱值</div>
                            <div class="info-value">{kline.get('macd_histogram', 'N/A'):.4f}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">信号类型</div>
                            <div class="info-value">{kline.get('signal_type', 'MACD背离')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">交易方向</div>
                            <div class="info-value">{direction_text}</div>
                        </div>
                    </div>
                    
                    <div class="trigger-reasons">
                        <strong>触发原因：</strong>{trigger_reasons}
                    </div>
                    
                    <div class="chart-container">
                        <h4>K线图+MACD柱状图</h4>
                        <p style="color: #666;">📊 图表将以附件形式发送或在支持内联图片的邮件客户端中显示</p>
                    </div>
                </div>
            </div>
        """
    
    html_content += """
            <div class="summary">
                <h3>使用说明</h3>
                <p><strong>如果看不到图表：</strong></p>
                <ul>
                    <li>检查邮件附件中的PNG图片文件</li>
                    <li>使用支持HTML邮件的客户端（Gmail、Outlook网页版）</li>
                    <li>检查邮件客户端的图片显示设置</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html_content

def main():
    """
    演示改进的邮件发送方案
    """
    print("=" * 60)
    print("改进的邮件发送方案演示")
    print("=" * 60)
    
    # 模拟数据
    abnormal_klines = [
        {
            'symbol': 'DCE.lh2509',
            'datetime': '2025-07-23 14:00:00',
            'open': 100.0,
            'high': 101.0,
            'low': 99.0,
            'close': 100.5,
            'price_change': -1.08,
            'direction': 'SHORT',
            'macd_histogram': 31.1026,
            'signal_type': 'MACDSHORT信号',
            'trigger_reason': ['MACD顶背离信号']
        }
    ]
    
    chart_data_list = [
        {
            'symbol': 'DCE.lh2509',
            'chart_base64': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='  # 1x1像素的测试图片
        }
    ]
    
    subject = "MACD背离信号提醒测试"
    
    print("1. 创建包含嵌入图片的邮件...")
    html_content = "测试HTML内容"  # 这里应该是完整的HTML内容
    embedded_msg = create_email_with_embedded_images(subject, html_content, chart_data_list)
    print(f"   邮件大小: {len(embedded_msg.as_string()) // 1024:.1f} KB")
    
    print("2. 创建包含图片附件的邮件...")
    html_no_images = build_email_content_without_images(abnormal_klines)
    attachment_msg = create_email_with_attachments(subject, html_no_images, chart_data_list)
    print(f"   邮件大小: {len(attachment_msg.as_string()) // 1024:.1f} KB")
    
    print("3. 创建纯文本邮件...")
    text_msg = create_simple_text_email(subject, abnormal_klines)
    print(f"   邮件大小: {len(text_msg.as_string()) // 1024:.1f} KB")
    
    print("\n✅ 三种邮件格式创建成功！")
    print("\n💡 建议的发送策略:")
    print("   1. 优先尝试发送嵌入图片的邮件")
    print("   2. 如果失败，发送附件版本")
    print("   3. 最后备用纯文本版本")

if __name__ == "__main__":
    main()
