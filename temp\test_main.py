from tqsdk import TqApi, TqAuth
from tqsdk.ta import BOLL, ATR, EMA
import datetime
import pandas as pd
import os
import time

def create_api_connection(max_retries=3, retry_delay=5):
    """
    创建天勤API连接，包含重试机制
    """
    for attempt in range(max_retries):
        try:
            print(f"正在连接天勤API... (尝试 {attempt + 1}/{max_retries})")
            api = TqApi(auth=TqAuth("a_1325", "Az4719197"))
            print("天勤API连接成功")
            return api
        except Exception as e:
            print(f"连接失败: {e}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print("达到最大重试次数，连接失败")
                return None

def safe_wait_update(api, timeout=30):
    """
    安全的等待更新函数，包含超时和异常处理
    """
    try:
        return api.wait_update(timeout=timeout)
    except Exception as e:
        print(f"等待更新时发生错误: {e}")
        return False

def check_price_change_threshold(kline, threshold_percent=0.1):
    """
    检测单根K线涨跌幅是否超过指定阈值
    """
    try:
        open_price = kline['open']
        close_price = kline['close']
        
        if open_price == 0:
            return False, 0.0, ""
        
        # 计算涨跌幅百分比
        change_percent = ((close_price - open_price) / open_price) * 100
        
        # 判断是否超过阈值
        if abs(change_percent) >= threshold_percent:
            if change_percent > 0:
                return True, change_percent, "上涨"
            else:
                return True, change_percent, "下跌"
        
        return False, change_percent, ""
        
    except Exception as e:
        print(f"计算涨跌幅时发生错误: {e}")
        return False, 0.0, ""

def test_basic_functionality():
    """
    测试基本功能
    """
    api = None
    try:
        # 创建API连接
        api = create_api_connection(max_retries=3, retry_delay=5)
        if api is None:
            print("无法连接到天勤API，测试退出")
            return
        
        # 测试单个合约
        symbol = "SHFE.cu2509"
        print(f"开始测试合约: {symbol}")
        
        # 获取K线数据
        klines = api.get_kline_serial(symbol, 60*10, data_length=10)  # 10分钟K线，获取10根
        print(f"成功获取 {len(klines)} 根K线数据")
        
        # 测试涨跌幅检测
        if len(klines) > 0:
            latest_kline = klines.iloc[-1]
            is_threshold_exceeded, change_percent, change_type = check_price_change_threshold(
                latest_kline, 0.1
            )
            
            print(f"最新K线信息:")
            print(f"  时间: {datetime.datetime.fromtimestamp(latest_kline['datetime'] / 1e9)}")
            print(f"  开盘价: {latest_kline['open']:.2f}")
            print(f"  收盘价: {latest_kline['close']:.2f}")
            print(f"  涨跌幅: {change_percent:+.2f}%")
            
            if is_threshold_exceeded:
                print(f"  *** 检测到{change_type}超过阈值！***")
            else:
                print(f"  涨跌幅未超过阈值")
        
        # 简单的监控循环（只运行几次测试）
        print("\n开始监控K线更新（测试模式，30秒后自动退出）...")
        start_time = time.time()
        update_count = 0
        
        while safe_wait_update(api, timeout=5):
            if time.time() - start_time > 30:  # 30秒后退出
                print("测试时间结束")
                break
                
            if api.is_changing(klines.iloc[-1], "datetime"):
                update_count += 1
                print(f"检测到K线更新 #{update_count}")
                
                latest_kline = klines.iloc[-1]
                is_threshold_exceeded, change_percent, change_type = check_price_change_threshold(
                    latest_kline, 0.1
                )
                
                if is_threshold_exceeded:
                    print(f"  *** 涨跌幅提醒: {symbol} {change_type} {change_percent:+.2f}% ***")
                else:
                    print(f"  涨跌幅: {change_percent:+.2f}%")
        
        print(f"测试完成，共检测到 {update_count} 次K线更新")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保API连接被正确关闭
        if api is not None:
            try:
                print("正在关闭API连接...")
                api.close()
                print("API连接已关闭")
            except Exception as e:
                print(f"关闭API连接时发生错误: {e}")

if __name__ == "__main__":
    test_basic_functionality()
