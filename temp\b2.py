import schedule
import time
import datetime
import subprocess
import os
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class TaskScheduler:
    def __init__(self):
        self.data_script_1 = "a.py"      # 数据获取脚本1
        self.data_script_2 = "a_1.py"    # 数据获取脚本2
        self.data_script_3 = "a_2.py"    # 数据获取脚本3
        self.analysis_script = "b1.py"   # 分析脚本
        self.running_process = None
        self.current_task = None         # 当前运行的任务名称

        # 定义每天的运行时间点（30分钟间隔，适配30分钟K线周期）
        self.schedule_times = [
            # 上午时段：09:00-11:30 (30分钟间隔)
            "09:00:00",
            "09:30:00",
            "10:00:00",
            "10:30:00",
            "11:00:00",
            "11:30:00",
            # 下午时段：13:30-15:00 (30分钟间隔)
            "13:30:00",
            "14:00:00",
            "14:30:00",
            "15:00:00",
            # 夜盘时段：21:00-23:00 (30分钟间隔)
            "21:30:00",
            "22:00:00",
            "22:30:00",
            "23:00:00",
        ]

    def run_single_script(self, script_path, script_name, timeout=300):
        """
        运行单个脚本并等待完成

        参数:
        script_path: 脚本路径
        script_name: 脚本名称（用于日志）
        timeout: 超时时间（秒），默认5分钟

        返回:
        bool: 是否成功完成
        """
        try:
            # 检查脚本文件是否存在
            if not os.path.exists(script_path):
                logging.error(f"脚本文件 {script_path} 不存在")
                return False

            logging.info(f"正在启动 {script_name}: {script_path}")
            self.current_task = script_name

            # 启动进程
            process = subprocess.Popen(
                [sys.executable, script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )

            self.running_process = process
            logging.info(f"{script_name} 已启动，进程ID: {process.pid}")

            # 等待进程完成
            try:
                stdout, stderr = process.communicate(timeout=timeout)
                return_code = process.returncode

                if return_code == 0:
                    logging.info(f"{script_name} 执行成功")
                    if stdout.strip():
                        logging.info(f"{script_name} 输出:\n{stdout}")
                    return True
                else:
                    logging.error(f"{script_name} 执行失败，退出码: {return_code}")
                    if stderr.strip():
                        logging.error(f"{script_name} 错误输出:\n{stderr}")
                    return False

            except subprocess.TimeoutExpired:
                logging.error(f"{script_name} 执行超时（{timeout}秒），正在终止...")
                process.terminate()
                time.sleep(5)
                if process.poll() is None:
                    process.kill()
                    logging.error(f"强制终止了 {script_name}")
                return False

        except Exception as e:
            logging.error(f"运行 {script_name} 时发生错误: {e}")
            return False
        finally:
            self.running_process = None
            self.current_task = None

    def run_task_sequence(self):
        """
        顺序运行数据获取和分析任务：a.py -> a_1.py -> a_2.py -> b1.py
        """
        try:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logging.info("=" * 60)
            logging.info(f"开始执行定时任务序列 - {current_time}")
            logging.info("=" * 60)

            # 如果有正在运行的进程，先终止它
            if self.running_process and self.running_process.poll() is None:
                logging.warning("检测到之前的任务仍在运行，正在终止...")
                self.running_process.terminate()
                time.sleep(5)
                if self.running_process.poll() is None:
                    self.running_process.kill()
                    logging.warning("强制终止了之前的任务")

            # 第一步：运行数据获取脚本1 (a.py)
            logging.info("第1步：运行数据获取脚本1（a.py）")
            data_success_1 = self.run_single_script(self.data_script_1, "数据获取脚本1(a.py)", timeout=1800)  # 30分钟

            if not data_success_1:
                logging.error("数据获取脚本1执行失败，跳过后续步骤")
                return False

            # 等待一小段时间确保数据文件完全写入
            logging.info("等待数据文件写入完成...")
            time.sleep(3)

            # 第二步：运行数据获取脚本2 (a_1.py)
            logging.info("第2步：运行数据获取脚本2（a_1.py）")
            data_success_2 = self.run_single_script(self.data_script_2, "数据获取脚本2(a_1.py)", timeout=1800)  # 30分钟

            if not data_success_2:
                logging.error("数据获取脚本2执行失败，跳过后续步骤")
                return False

            # 等待一小段时间确保数据文件完全写入
            logging.info("等待数据文件写入完成...")
            time.sleep(3)

            # 第三步：运行数据获取脚本3 (a_2.py)
            logging.info("第3步：运行数据获取脚本3（a_2.py）")
            data_success_3 = self.run_single_script(self.data_script_3, "数据获取脚本3(a_2.py)", timeout=1800)  # 30分钟

            if not data_success_3:
                logging.error("数据获取脚本3执行失败，跳过分析步骤")
                return False

            # 等待一小段时间确保数据文件完全写入
            logging.info("等待数据文件写入完成...")
            time.sleep(3)

            # 第四步：运行分析脚本 (b1.py)
            logging.info("第4步：运行K线分析脚本（b1.py）")
            analysis_success = self.run_single_script(self.analysis_script, "K线分析脚本(b1.py)", timeout=600)  # 10分钟

            if analysis_success:
                logging.info("=" * 60)
                logging.info("任务序列执行完成 - 全部成功")
                logging.info("=" * 60)
                return True
            else:
                logging.error("=" * 60)
                logging.error("任务序列执行完成 - 分析步骤失败")
                logging.error("=" * 60)
                return False

        except Exception as e:
            logging.error(f"执行任务序列时发生错误: {e}")
            return False

    def setup_schedule(self):
        """
        设置定时任务
        """
        logging.info("正在设置定时任务...")

        for time_str in self.schedule_times:
            schedule.every().day.at(time_str).do(self.run_task_sequence)
            logging.info(f"已设置定时任务: 每天 {time_str} (a.py -> a_1.py -> a_2.py -> b1.py)")

        logging.info(f"总共设置了 {len(self.schedule_times)} 个定时任务")

    def get_next_run_time(self):
        """
        获取下一次运行时间
        """
        next_job = schedule.next_run()
        if next_job:
            return next_job.strftime('%Y-%m-%d %H:%M:%S')
        return "无"

    def show_schedule_info(self):
        """
        显示调度信息
        """
        logging.info("=== 定时任务调度信息 ===")
        logging.info(f"数据获取脚本1: {self.data_script_1}")
        logging.info(f"数据获取脚本2: {self.data_script_2}")
        logging.info(f"数据获取脚本3: {self.data_script_3}")
        logging.info(f"分析脚本: {self.analysis_script}")
        logging.info(f"任务数量: {len(self.schedule_times)}")
        logging.info(f"执行顺序: {self.data_script_1} -> {self.data_script_2} -> {self.data_script_3} -> {self.analysis_script}")
        logging.info(f"下次运行时间: {self.get_next_run_time()}")
        logging.info("每日运行时间点:")
        for i, time_str in enumerate(self.schedule_times, 1):
            logging.info(f"  {i:2d}. {time_str}")
        logging.info("========================")

    def run_scheduler(self):
        """
        运行调度器主循环
        """
        try:
            self.setup_schedule()
            self.show_schedule_info()

            logging.info("定时任务调度器已启动，按 Ctrl+C 停止")

            while True:
                schedule.run_pending()
                time.sleep(1)  # 每秒检查一次

        except KeyboardInterrupt:
            logging.info("收到停止信号，正在关闭调度器...")

            # 终止正在运行的进程
            if self.running_process and self.running_process.poll() is None:
                logging.info("正在终止运行中的任务...")
                self.running_process.terminate()
                time.sleep(5)
                if self.running_process.poll() is None:
                    self.running_process.kill()
                    logging.info("强制终止了运行中的任务")

            logging.info("调度器已停止")

        except Exception as e:
            logging.error(f"调度器运行时发生错误: {e}")

    def test_run(self):
        """
        手动测试运行任务序列
        """
        logging.info("手动测试运行任务序列...")
        self.run_task_sequence()

    def test_data_script(self):
        """
        单独测试数据获取脚本
        """
        logging.info("单独测试数据获取脚本（全部品种）...")
        success1 = self.run_single_script(self.data_script_1, "数据获取脚本1(a.py)", timeout=1800)  # 30分钟
        success2 = self.run_single_script(self.data_script_2, "数据获取脚本2(a_1.py)", timeout=1800)  # 30分钟
        success3 = self.run_single_script(self.data_script_3, "数据获取脚本3(a_2.py)", timeout=1800)  # 30分钟

        if success1 and success2 and success3:
            logging.info("所有数据获取脚本测试成功")
        else:
            logging.error("部分数据获取脚本测试失败")
            logging.error(f"a.py: {'成功' if success1 else '失败'}")
            logging.error(f"a_1.py: {'成功' if success2 else '失败'}")
            logging.error(f"a_2.py: {'成功' if success3 else '失败'}")

    def test_analysis_script(self):
        """
        单独测试分析脚本
        """
        logging.info("单独测试分析脚本（全部品种）...")
        success = self.run_single_script(self.analysis_script, "K线分析脚本(b1.py)", timeout=600)  # 10分钟
        if success:
            logging.info("分析脚本测试成功")
        else:
            logging.error("分析脚本测试失败")

    def check_script_status(self):
        """
        检查脚本运行状态
        """
        if self.running_process is None:
            return "未运行"
        elif self.running_process.poll() is None:
            current_task = self.current_task if self.current_task else "未知任务"
            return f"运行中: {current_task} (PID: {self.running_process.pid})"
        else:
            return f"已结束 (退出码: {self.running_process.returncode})"

    def show_status(self):
        """
        显示当前状态
        """
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        logging.info("=== 当前状态 ===")
        logging.info(f"当前时间: {current_time}")
        logging.info(f"任务状态: {self.check_script_status()}")
        logging.info(f"下次运行: {self.get_next_run_time()}")
        logging.info("================")

def main():
    """
    主函数
    """
    print("=" * 60)
    print("K线数据获取与分析定时调度器（全品种监控）")
    print("=" * 60)

    # 检查是否需要安装schedule库
    try:
        import schedule
    except ImportError:
        print("错误: 缺少 schedule 库")
        print("请运行: pip install schedule")
        return

    scheduler = TaskScheduler()

    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "test":
            print("执行测试运行任务序列...")
            scheduler.test_run()
            return
        elif command == "test-data":
            print("单独测试数据获取脚本...")
            scheduler.test_data_script()
            return
        elif command == "test-analysis":
            print("单独测试分析脚本...")
            scheduler.test_analysis_script()
            return
        elif command == "status":
            print("显示状态信息...")
            scheduler.show_status()
            return
        elif command == "info":
            print("显示调度信息...")
            scheduler.setup_schedule()
            scheduler.show_schedule_info()
            return
        else:
            print("可用命令:")
            print("  python b2.py             - 启动定时调度器")
            print("  python b2.py test        - 手动测试运行任务序列")
            print("  python b2.py test-data   - 单独测试数据获取脚本(a.py, a_1.py, a_2.py)")
            print("  python b2.py test-analysis - 单独测试分析脚本(b1.py)")
            print("  python b2.py status      - 显示当前状态")
            print("  python b2.py info        - 显示调度信息")
            print("")
            print("任务序列: a.py -> a_1.py -> a_2.py -> b1.py")
            print("说明: 按顺序执行数据获取脚本，最后运行分析脚本")
            print("监控范围: 全部品种（不再限制为5个）")
            print("超时设置: 数据获取30分钟，分析10分钟")
            return

    # 启动调度器
    scheduler.run_scheduler()

if __name__ == "__main__":
    main()