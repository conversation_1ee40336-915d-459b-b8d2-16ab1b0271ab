#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MACD峰值标记功能
"""

import os
import pandas as pd
import numpy as np
import datetime
from b1_macd_1 import KLineAnalyzer

def create_test_data_with_clear_signals():
    """
    创建包含明确MACD背离信号的测试数据
    """
    print("创建包含明确MACD背离信号的测试数据...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    test_dir = f"test_peak_marking/{today}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    
    symbols = ["TEST.long_signal", "TEST.short_signal"]
    
    for i, symbol in enumerate(symbols):
        print(f"生成 {symbol} 的数据...")
        
        # 生成80根30分钟K线数据
        n_bars = 80
        base_price = 100.0
        
        if i == 0:
            # 做多信号：价格下跌，MACD改善（底背离）
            price_trend = []
            # 前50根K线：正常波动
            for j in range(50):
                change = np.random.normal(0, 0.2)
                price_trend.append(change)
            
            # 第51-65根K线：价格下跌，MACD也下跌（第一个波峰）
            for j in range(15):
                change = np.random.normal(-0.8, 0.1)
                price_trend.append(change)
            
            # 第66-80根K线：价格继续下跌但MACD改善（第二个波峰，形成底背离）
            for j in range(15):
                change = np.random.normal(-0.3, 0.05)
                price_trend.append(change)
        else:
            # 做空信号：价格上涨，MACD恶化（顶背离）
            price_trend = []
            # 前50根K线：正常波动
            for j in range(50):
                change = np.random.normal(0, 0.2)
                price_trend.append(change)
            
            # 第51-65根K线：价格上涨，MACD也上涨（第一个波峰）
            for j in range(15):
                change = np.random.normal(0.8, 0.1)
                price_trend.append(change)
            
            # 第66-80根K线：价格继续上涨但MACD恶化（第二个波峰，形成顶背离）
            for j in range(15):
                change = np.random.normal(0.3, 0.05)
                price_trend.append(change)
        
        # 生成OHLC数据
        opens, highs, lows, closes, volumes = [], [], [], [], []
        current_price = base_price
        
        for j, change in enumerate(price_trend):
            open_price = current_price
            close_price = open_price * (1 + change / 100)
            
            if change > 0:
                high_price = close_price * (1 + np.random.uniform(0, 0.1) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.05) / 100)
            else:
                high_price = open_price * (1 + np.random.uniform(0, 0.05) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.1) / 100)
            
            volume = np.random.randint(5000, 20000)
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 创建30分钟时间序列
        start_time = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        time_list = []
        current_time = start_time
        
        for j in range(n_bars):
            time_list.append(current_time)
            current_time += datetime.timedelta(minutes=30)
            
            # 跳过非交易时间（简化处理）
            if current_time.hour == 12:
                current_time = current_time.replace(hour=13, minute=30)
            elif current_time.hour >= 15 and current_time.hour < 21:
                current_time = current_time.replace(hour=21, minute=0)
            elif current_time.hour >= 24:
                current_time = current_time.replace(hour=9, minute=0) + datetime.timedelta(days=1)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': time_list,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes
        })

        # 添加必要的指标列（使用简单计算）
        # ATR指标
        df['tr'] = np.maximum(df['high'] - df['low'],
                             np.maximum(abs(df['high'] - df['close'].shift(1)),
                                       abs(df['low'] - df['close'].shift(1))))
        df['atr'] = df['tr'].rolling(window=14).mean()

        # BOLL指标
        df['boll_middle'] = df['close'].rolling(window=20).mean()
        df['boll_std'] = df['close'].rolling(window=20).std()
        df['boll_upper'] = df['boll_middle'] + 2 * df['boll_std']
        df['boll_lower'] = df['boll_middle'] - 2 * df['boll_std']

        # 填充NaN值
        df = df.fillna(method='bfill').fillna(method='ffill')
        
        # 保存到Excel文件
        file_name = f"{symbol.replace('.', '_')}_kline_indicators.xlsx"
        file_path = os.path.join(test_dir, file_name)
        df.to_excel(file_path, index=False)
        
        print(f"  文件: {file_name}")
        print(f"  K线数量: {len(df)} 根")
        print(f"  价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    print(f"\n测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_peak_marking():
    """
    测试MACD峰值标记功能
    """
    print("=" * 60)
    print("测试MACD峰值标记功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_test_data_with_clear_signals()
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="test_peak_marking", kline_period="30min")
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  K线周期: {analyzer.kline_period}")
    print(f"  K线图历史数量: {analyzer.chart_history_bars} 根")
    
    # 运行分析
    print(f"\n开始分析...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n分析结果:")
    print(f"  信号品种总数: {result['total_abnormal']}")
    print(f"  做多信号: {result['long_count']} 个品种")
    print(f"  做空信号: {result['short_count']} 个品种")
    
    # 生成邮件内容进行测试
    if result['abnormal_klines']:
        print(f"\n生成邮件内容...")
        email_content = analyzer.build_email_content(result['abnormal_klines'])
        
        # 保存邮件内容到HTML文件
        html_file = "test_peak_marking_preview.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(email_content)
        
        print(f"邮件内容已保存到: {html_file}")
        
        # 显示信号详情
        print(f"\nMACD背离信号详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     入场时间: {kline['datetime']}")
            print(f"     MACD柱值: {kline.get('macd_histogram', 'N/A'):.4f}")
            print(f"     入场点索引: {kline.get('entry_index', 'N/A')}")
            print(f"     当前峰值索引: {kline.get('current_peak_idx', 'N/A')}")
            print(f"     前一峰值索引: {kline.get('prev_peak_idx', 'N/A')}")
            print(f"     信号类型: {kline.get('signal_type', 'N/A')}")
            print(f"     交易方向: {kline.get('direction', 'N/A')}")
        
        # 检查图表是否包含峰值标记
        has_chart = "data:image/png;base64," in email_content
        chart_count = email_content.count("data:image/png;base64,")
        
        print(f"\n图表检查结果:")
        print(f"  包含图表: {'是' if has_chart else '否'}")
        print(f"  图表数量: {chart_count}")
        
        success = True
    else:
        print("未检测到MACD背离信号")
        success = False
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_peak_marking"):
        shutil.rmtree("test_peak_marking")
        print(f"\n测试数据已清理")
    
    return success

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("MACD峰值标记功能测试")
    print("=" * 60)
    
    try:
        # 检查依赖
        try:
            import matplotlib.pyplot as plt
            print("✓ matplotlib 可用")
        except ImportError:
            print("✗ matplotlib 不可用，请安装: pip install matplotlib")
            return
        
        # 运行测试
        success = test_peak_marking()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if success:
            print("🎉 MACD峰值标记功能测试成功！")
            print("✓ 做多和做空信号检测正常")
            print("✓ MACD峰值位置标记正常")
            print("✓ K线图+MACD柱状图生成正常")
            print("✓ 入场点标记正常")
            print("✓ 邮件内容生成正常")
            print("\n请用浏览器打开 test_peak_marking_preview.html 查看效果")
            print("图表中应该包含：")
            print("  - 黄色边框：入场点标记")
            print("  - 蓝色虚线边框：峰值1标记")
            print("  - 绿色虚线边框：峰值2标记")
            print("  - MACD柱状图中对应的颜色标记")
        else:
            print("⚠️  测试未完成或失败")
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
