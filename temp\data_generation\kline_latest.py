from tqsdk import TqApi, TqAuth
from tqsdk.ta import ATR, BOLL
import datetime
import pandas as pd
import os

## 推送实时k线数据
api = TqApi(auth=TqAuth("a_1325", "Az4719197"))

# 读取合约列表
try:
    symbols_df = pd.read_excel("generated_symbols_9_2025.xlsx")
    if not symbols_df.empty and symbols_df.shape[1] > 0:
        # 获取前5个合约代码
        symbol_list = symbols_df.iloc[:5, 0].astype(str).tolist()
        print(f"将处理以下合约: {symbol_list}")
    else:
        print("Excel文件为空或没有列。")
        symbol_list = []
except FileNotFoundError:
    print("错误: generated_symbols_9_2025.xlsx 文件未找到。")
    symbol_list = []
except Exception as e:
    print(f"读取Excel文件时发生错误: {e}")
    symbol_list = []

if not symbol_list:
    print("没有合约可供处理。程序将退出。")
    exit()

T_period = 60*15  # 5分钟为一个K线周期
data_len = 100

# 为每个合约创建数据存储
contract_data = {}

base_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
today = datetime.datetime.now().strftime('%Y%m%d')
save_dir = os.path.join(base_dir, today)

if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 初始化所有合约的K线数据
for rt_symbol in symbol_list:
    print(f"\n开始处理合约: {rt_symbol}")
    
    # 创建保存K线数据的DataFrame
    kline_data = pd.DataFrame(columns=[
        'datetime', 'open', 'high', 'low', 'close', 'volume',
        'atr_tr', 'atr',  # ATR指标
        'boll_upper', 'boll_middle', 'boll_lower'  # BOLL指标
    ])
    
    # 创建Excel文件名
    excel_filename = f"{rt_symbol.replace('.', '_')}_kline_data.xlsx"
    excel_path = os.path.join(save_dir, excel_filename)
    print(f"数据将保存到文件: {excel_path}")
    
    # 获取初始K线数据
    klines = api.get_kline_serial(rt_symbol, T_period, data_length=data_len)
    if len(klines) > 0:
        # 计算技术指标
        atr = ATR(klines, 14)  # 14周期ATR
        boll = BOLL(klines, 20, 2)  # 20周期，2倍标准差BOLL
        
        # 保存所有初始K线数据
        for i in range(len(klines)):
            kline = klines.iloc[i]
            current_time = datetime.datetime.fromtimestamp(kline['datetime'] / 1e9)
            readable_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 创建K线数据行
            kline_row = {
                'datetime': readable_time,
                'open': kline['open'],
                'high': kline['high'],
                'low': kline['low'],
                'close': kline['close'],
                'volume': kline['volume'],
                'atr_tr': atr.tr.iloc[i],
                'atr': atr.atr.iloc[i],
                'boll_upper': boll["top"].iloc[i],
                'boll_middle': boll["mid"].iloc[i],
                'boll_lower': boll["bottom"].iloc[i]
            }
            
            # 添加到DataFrame
            kline_data = pd.concat([kline_data, pd.DataFrame([kline_row])], ignore_index=True)
        
        # 保存到Excel文件
        kline_data.to_excel(excel_path, index=False)
        
        # 记录上一根K线的时间
        last_kline_time = datetime.datetime.fromtimestamp(klines.iloc[-1]['datetime'] / 1e9)
        
        print(f"\n已保存初始{len(klines)}条K线数据")
        print(f"最新K线数据 - 时间: {last_kline_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"开盘价: {klines.iloc[-1]['open']}")
        print(f"最高价: {klines.iloc[-1]['high']}")
        print(f"最低价: {klines.iloc[-1]['low']}")
        print(f"收盘价: {klines.iloc[-1]['close']}")
        print(f"成交量: {klines.iloc[-1]['volume']}")
        print(f"ATR(TR): {atr.tr.iloc[-1]:.2f}")
        print(f"ATR: {atr.atr.iloc[-1]:.2f}")
        print(f"BOLL上轨: {boll['top'].iloc[-1]:.2f}")
        print(f"BOLL中轨: {boll['mid'].iloc[-1]:.2f}")
        print(f"BOLL下轨: {boll['bottom'].iloc[-1]:.2f}")
        print(f"初始数据已保存到: {excel_path}")
        
        # 存储合约数据
        contract_data[rt_symbol] = {
            'kline_data': kline_data,
            'last_kline_time': last_kline_time,
            'excel_filename': excel_filename,
            'klines': klines  # 保存K线序列对象
        }

print("\n开始实时数据更新...")

while api.wait_update():
    for rt_symbol in symbol_list:
        try:
            # 检查K线时间是否有变化
            if api.is_changing(contract_data[rt_symbol]['klines'].iloc[-1], "datetime"):
                # 获取最新K线数据
                klines = api.get_kline_serial(rt_symbol, T_period, data_length=data_len)
                latest_kline = klines.iloc[-1]
                current_time = datetime.datetime.fromtimestamp(latest_kline['datetime'] / 1e9)
                
                # 计算技术指标
                atr = ATR(klines, 14)
                boll = BOLL(klines, 20, 2)
                
                # 创建新的K线数据行
                new_kline = {
                    'datetime': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'open': latest_kline['open'],
                    'high': latest_kline['high'],
                    'low': latest_kline['low'],
                    'close': latest_kline['close'],
                    'volume': latest_kline['volume'],
                    'atr_tr': atr.tr.iloc[-1],
                    'atr': atr.atr.iloc[-1],
                    'boll_upper': boll["top"].iloc[-1],
                    'boll_middle': boll["mid"].iloc[-1],
                    'boll_lower': boll["bottom"].iloc[-1]
                }
                
                # 添加到DataFrame
                contract_data[rt_symbol]['kline_data'] = pd.concat(
                    [contract_data[rt_symbol]['kline_data'], pd.DataFrame([new_kline])], 
                    ignore_index=True
                )
                
                # 保存到Excel文件
                contract_data[rt_symbol]['kline_data'].to_excel(
                    os.path.join(save_dir, contract_data[rt_symbol]['excel_filename']), 
                    index=False
                )
                
                # 更新K线序列对象
                contract_data[rt_symbol]['klines'] = klines
                
                # 打印新K线信息
                print(f"\n合约 {rt_symbol} 新K线生成 - 时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"开盘价: {latest_kline['open']}")
                print(f"最高价: {latest_kline['high']}")
                print(f"最低价: {latest_kline['low']}")
                print(f"收盘价: {latest_kline['close']}")
                print(f"成交量: {latest_kline['volume']}")
                print(f"ATR(TR): {atr.tr.iloc[-1]:.2f}")
                print(f"ATR: {atr.atr.iloc[-1]:.2f}")
                print(f"BOLL上轨: {boll['top'].iloc[-1]:.2f}")
                print(f"BOLL中轨: {boll['mid'].iloc[-1]:.2f}")
                print(f"BOLL下轨: {boll['bottom'].iloc[-1]:.2f}")
                print(f"数据已保存到: {contract_data[rt_symbol]['excel_filename']}")
        except Exception as e:
            print(f"处理合约 {rt_symbol} 时发生错误: {e}")
            continue

print("\n程序结束，所有数据已保存到Excel文件。")
api.close()