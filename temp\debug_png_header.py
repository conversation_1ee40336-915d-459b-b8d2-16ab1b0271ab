#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PNG头部问题
"""

import base64
import os
import pandas as pd
from b1_macd_1 import KLineAnalyzer

def debug_png_header():
    """
    调试PNG头部问题
    """
    print("=" * 60)
    print("调试PNG头部问题")
    print("=" * 60)
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="data", kline_period="30min")
    
    # 读取测试数据
    data_files = [f for f in os.listdir(analyzer.target_path) if f.endswith('.xlsx')]
    if not data_files:
        print("未找到数据文件")
        return
    
    test_file = data_files[0]
    file_path = os.path.join(analyzer.target_path, test_file)
    df = pd.read_excel(file_path)
    symbol = test_file.replace('_kline_data.xlsx', '').replace('_', '.')
    
    print(f"使用测试数据: {symbol}")
    
    # 生成图片
    chart_base64 = analyzer.create_kline_chart(
        df, symbol, 
        abnormal_index=-5, 
        peak_indices=[-8, -25]
    )
    
    if chart_base64:
        print(f"Base64长度: {len(chart_base64)}")
        
        # 解码base64
        try:
            decoded = base64.b64decode(chart_base64)
            print(f"解码后大小: {len(decoded)} 字节")
            
            # 检查PNG头部
            png_signature = b'\x89PNG\r\n\x1a\n'
            actual_header = decoded[:8]
            
            print(f"期望的PNG头部: {png_signature}")
            print(f"实际的头部: {actual_header}")
            print(f"头部匹配: {'✓' if actual_header == png_signature else '✗'}")
            
            # 显示头部的十六进制
            print(f"期望头部(hex): {png_signature.hex()}")
            print(f"实际头部(hex): {actual_header.hex()}")
            
            # 检查是否是有效的图片格式
            if actual_header.startswith(b'\x89PNG'):
                print("✓ 这是一个PNG文件")
            elif actual_header.startswith(b'\xff\xd8\xff'):
                print("这是一个JPEG文件")
            elif actual_header.startswith(b'GIF'):
                print("这是一个GIF文件")
            else:
                print("✗ 未知的图片格式")
            
            # 保存为文件进行验证
            with open("test_chart.png", "wb") as f:
                f.write(decoded)
            print("图片已保存为 test_chart.png，请手动检查")
            
            # 尝试用PIL验证
            try:
                from PIL import Image
                img = Image.open("test_chart.png")
                print(f"✓ PIL验证成功: {img.format}, {img.size}")
            except ImportError:
                print("PIL未安装，无法验证图片")
            except Exception as e:
                print(f"✗ PIL验证失败: {e}")
                
        except Exception as e:
            print(f"Base64解码失败: {e}")
    else:
        print("图片生成失败")

def fix_png_header_check():
    """
    修复PNG头部检查
    """
    print("\n" + "=" * 60)
    print("修复PNG头部检查")
    print("=" * 60)
    
    # 正确的PNG签名
    png_signature = b'\x89PNG\r\n\x1a\n'
    
    print("PNG文件签名说明:")
    print(f"  完整签名: {png_signature}")
    print(f"  十六进制: {png_signature.hex()}")
    print(f"  前4字节: {png_signature[:4]}")
    print(f"  简化检查: 只检查前4字节 \\x89PNG")
    
    # 测试不同的检查方法
    test_data = b'\x89PNG\r\n\x1a\n' + b'test data'
    
    print(f"\n测试数据: {test_data[:8].hex()}")
    print(f"完整匹配: {test_data.startswith(png_signature)}")
    print(f"前4字节匹配: {test_data.startswith(b'\\x89PNG')}")
    print(f"字符串匹配: {test_data.startswith(b'\\x89PNG')}")

if __name__ == "__main__":
    debug_png_header()
    fix_png_header_check()
