#!/usr/bin/env python3
"""
测试30分钟K线周期和50根历史K线功能
"""

import os
import pandas as pd
import numpy as np
import datetime
from b1 import KLineAnalyzer

def create_30min_test_data():
    """
    创建30分钟K线测试数据（60根K线，确保有足够的历史数据）
    """
    print("创建30分钟K线测试数据...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    test_dir = f"test_30min/{today}"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试数据
    np.random.seed(42)
    
    symbols = ["TEST.30min1", "TEST.30min2"]
    
    for i, symbol in enumerate(symbols):
        print(f"生成 {symbol} 的30分钟K线数据...")
        
        # 生成60根30分钟K线数据（确保有足够历史数据）
        n_bars = 60
        base_price = 100.0
        
        # 生成价格变化序列
        price_changes = np.random.normal(0, 0.4, n_bars)
        
        # 倒数第二根K线设置为异常
        if i == 0:
            # 第一个品种：ATR超标
            price_changes[-2] = 1.5
        else:
            # 第二个品种：BOLL突破
            price_changes[-2] = 0.8
        
        # 最后一根K线正常
        price_changes[-1] = 0.1
        
        # 生成OHLC数据
        opens, highs, lows, closes, volumes = [], [], [], [], []
        current_price = base_price
        
        for j in range(n_bars):
            open_price = current_price
            change = price_changes[j]
            close_price = open_price * (1 + change / 100)
            
            if change > 0:
                high_price = close_price * (1 + np.random.uniform(0, 0.3) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.2) / 100)
            else:
                high_price = open_price * (1 + np.random.uniform(0, 0.2) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.3) / 100)
            
            volume = np.random.randint(5000, 20000)  # 30分钟K线成交量更大
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 计算ATR指标
        atr_tr_values = []
        atr_values = []
        
        for j in range(n_bars):
            if j == 0:
                atr_tr = highs[j] - lows[j]
                atr = atr_tr
            else:
                tr1 = highs[j] - lows[j]
                tr2 = abs(highs[j] - closes[j-1])
                tr3 = abs(lows[j] - closes[j-1])
                atr_tr = max(tr1, tr2, tr3)
                
                if j < 14:
                    atr = sum(atr_tr_values + [atr_tr]) / (j + 1)
                else:
                    atr = sum(atr_tr_values[-13:] + [atr_tr]) / 14
            
            atr_tr_values.append(round(atr_tr, 4))
            atr_values.append(round(atr, 4))
        
        # 人为调整倒数第二根K线的ATR
        if i == 0:  # 第一个品种ATR超标
            target_atr = abs(closes[-2] - opens[-2]) / 2.5  # 使实体/ATR比值为2.5
            atr_values[-2] = round(target_atr, 4)
        
        # 计算BOLL指标
        boll_upper, boll_middle, boll_lower = [], [], []
        
        for j in range(n_bars):
            if j < 20:
                middle = closes[j]
                std = 0.8  # 30分钟K线波动更大
            else:
                period_closes = closes[max(0, j-19):j+1]
                middle = sum(period_closes) / len(period_closes)
                variance = sum((x - middle) ** 2 for x in period_closes) / len(period_closes)
                std = variance ** 0.5
            
            upper = middle + 2 * std
            lower = middle - 2 * std
            
            boll_upper.append(round(upper, 2))
            boll_middle.append(round(middle, 2))
            boll_lower.append(round(lower, 2))
        
        # 人为调整倒数第二根K线的BOLL位置
        if i == 1:  # 第二个品种BOLL突破
            boll_range = boll_upper[-2] - boll_lower[-2]
            target_close = boll_lower[-2] + boll_range * 0.98  # 接近上轨
            closes[-2] = round(target_close, 2)
        
        # 创建30分钟时间序列
        start_time = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        time_list = []
        current_time = start_time
        
        for j in range(n_bars):
            time_list.append(current_time)
            current_time += datetime.timedelta(minutes=30)
            
            # 跳过非交易时间（简化处理）
            if current_time.hour == 12:  # 跳过12:00-13:30
                current_time = current_time.replace(hour=13, minute=30)
            elif current_time.hour >= 15 and current_time.hour < 21:  # 跳过15:00-21:00
                current_time = current_time.replace(hour=21, minute=0)
            elif current_time.hour >= 24:  # 跨日处理
                current_time = current_time.replace(hour=9, minute=0) + datetime.timedelta(days=1)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': time_list,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'atr_tr': atr_tr_values,
            'atr': atr_values,
            'boll_upper': boll_upper,
            'boll_middle': boll_middle,
            'boll_lower': boll_lower
        })
        
        # 保存到Excel文件
        file_name = f"{symbol.replace('.', '_')}_kline_indicators.xlsx"
        file_path = os.path.join(test_dir, file_name)
        df.to_excel(file_path, index=False)
        
        # 显示信息
        second_last_row = df.iloc[-2]
        actual_change = ((second_last_row['close'] - second_last_row['open']) / second_last_row['open']) * 100
        actual_atr_ratio = abs(second_last_row['close'] - second_last_row['open']) / second_last_row['atr']
        actual_boll_pos = (second_last_row['close'] - second_last_row['boll_lower']) / (second_last_row['boll_upper'] - second_last_row['boll_lower'])
        
        print(f"  文件: {file_name}")
        print(f"  K线数量: {len(df)} 根")
        print(f"  时间范围: {df.iloc[0]['datetime']} 到 {df.iloc[-1]['datetime']}")
        print(f"  倒数第二根K线时间: {second_last_row['datetime']}")
        print(f"  实际涨跌幅: {actual_change:+.2f}%")
        print(f"  实际ATR比值: {actual_atr_ratio:.2f}")
        print(f"  实际BOLL位置: {actual_boll_pos:.2f}")
    
    print(f"\n30分钟K线测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_30min_kline_analysis():
    """
    测试30分钟K线分析功能
    """
    print("=" * 60)
    print("测试30分钟K线周期和50根历史K线功能")
    print("=" * 60)
    
    # 创建测试数据
    test_dir = create_30min_test_data()
    
    # 创建分析器（指定30分钟周期）
    analyzer = KLineAnalyzer(data_dir="test_30min", kline_period="30min")
    
    # 设置阈值
    analyzer.atr_multiplier = 2.0
    analyzer.boll_threshold = 0.95
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  K线周期: {analyzer.kline_period}")
    print(f"  K线图历史数量: {analyzer.chart_history_bars} 根")
    print(f"  ATR倍数阈值: {analyzer.atr_multiplier}")
    print(f"  BOLL突破阈值: {analyzer.boll_threshold}")
    
    # 运行分析
    print(f"\n开始分析...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n分析结果:")
    print(f"  异常品种总数: {result['total_abnormal']}")
    print(f"  ATR超标: {result['atr_exceeded_count']} 个品种")
    print(f"  BOLL突破: {result['boll_breakthrough_count']} 个品种")
    
    # 生成邮件内容进行测试
    if result['abnormal_klines']:
        print(f"\n生成邮件内容...")
        email_content = analyzer.build_email_content(result['abnormal_klines'])
        
        # 保存邮件内容到HTML文件
        html_file = "test_30min_email_preview.html"
        with open(html_file, "w", encoding="utf-8") as f:
            f.write(email_content)
        
        print(f"邮件内容已保存到: {html_file}")
        
        # 显示异常K线详情
        print(f"\n异常K线详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     开盘时间: {kline['datetime']}")
            print(f"     ATR比值: {kline['body_atr_ratio']:.2f}")
            print(f"     BOLL位置: {kline['boll_position']:.2f}")
            print(f"     触发原因: {'; '.join(kline['trigger_reason'])}")
            
            # 检查K线图数据
            if 'kline_data' in kline and kline['kline_data'] is not None:
                kline_count = len(kline['kline_data'])
                print(f"     K线图数据: {kline_count} 根K线")
                if kline_count >= analyzer.chart_history_bars:
                    print(f"     ✓ 满足50根历史K线要求")
                else:
                    print(f"     ⚠ 历史K线不足50根")
        
        success = True
    else:
        print("未检测到异常K线")
        success = False
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_30min"):
        shutil.rmtree("test_30min")
        print(f"\n测试数据已清理")
    
    return success

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("30分钟K线周期和50根历史K线测试")
    print("=" * 60)
    
    try:
        # 运行测试
        success = test_30min_kline_analysis()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if success:
            print("🎉 30分钟K线功能测试成功！")
            print("✓ 30分钟K线周期配置正确")
            print("✓ 50根历史K线数据充足")
            print("✓ K线图生成正常")
            print("✓ 时间轴显示正确")
            print("\n请查看 test_30min_email_preview.html 文件预览邮件效果")
        else:
            print("⚠️  测试未完成或失败")
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
