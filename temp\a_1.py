from tqsdk import TqApi, TqAuth
from tqsdk.ta import ATR, BOLL
import datetime
import pandas as pd
import os
import warnings
warnings.filterwarnings('ignore', category=FutureWarning)

## 推送实时k线数据
api = TqApi(auth=TqAuth("a_1325", "Az4719197"))

# 读取合约列表
try:
    symbols_df = pd.read_excel("generated_symbols_9_2025.xlsx")
    if not symbols_df.empty and symbols_df.shape[1] > 0:
        # 获取第27-52个合约（共26个）
        symbol_list = symbols_df.iloc[26:52, 0].astype(str).tolist()
        print(f"a_1.py将处理第27-52个合约，共 {len(symbol_list)} 个合约")
    else:
        print("Excel文件为空或没有列。")
        symbol_list = []
except FileNotFoundError:
    print("错误: generated_symbols_10_2025.xlsx 文件未找到。")
    symbol_list = []
except Exception as e:
    print(f"读取Excel文件时发生错误: {e}")
    symbol_list = []

if not symbol_list:
    print("没有合约可供处理。程序将退出。")
    exit()

T_period = 60*30  # 15分钟为一个K线周期
data_len = 100

# 为每个合约创建数据存储
contract_data = {}

base_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
today = datetime.datetime.now().strftime('%Y%m%d')
save_dir = os.path.join(base_dir, today)

if not os.path.exists(save_dir):
    os.makedirs(save_dir)

# 创建一个列表来存储处理失败的品种
failed_symbols = []

# 处理每个合约
for rt_symbol in symbol_list:
    try:
        print(f"\n开始处理合约: {rt_symbol}")
        
        # 创建保存K线数据的DataFrame
        kline_data = pd.DataFrame(columns=[
            'datetime', 'open', 'high', 'low', 'close', 'volume',
            'atr_tr', 'atr',  # ATR指标
            'boll_upper', 'boll_middle', 'boll_lower'  # BOLL指标
        ])
        
        # 创建Excel文件名（使用合约代码和当前时间）
        excel_filename = f"{rt_symbol.replace('.', '_')}_kline_data.xlsx"
        excel_path = os.path.join(save_dir, excel_filename)
        print(f"数据将保存到文件: {excel_path}")
        
        # 获取初始K线数据
        klines = api.get_kline_serial(rt_symbol, T_period, data_length=data_len)
        if len(klines) > 0:
            # 计算技术指标
            atr = ATR(klines, 14)  # 14周期ATR
            boll = BOLL(klines, 20, 2)  # 20周期，2倍标准差BOLL
            
            # 保存所有初始K线数据
            for i in range(len(klines)):
                kline = klines.iloc[i]
                current_time = datetime.datetime.fromtimestamp(kline['datetime'] / 1e9)
                readable_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # 创建K线数据行
                kline_row = {
                    'datetime': readable_time,
                    'open': kline['open'],
                    'high': kline['high'],
                    'low': kline['low'],
                    'close': kline['close'],
                    'volume': kline['volume'],
                    'atr_tr': atr.tr.iloc[i],
                    'atr': atr.atr.iloc[i],
                    'boll_upper': boll["top"].iloc[i],
                    'boll_middle': boll["mid"].iloc[i],
                    'boll_lower': boll["bottom"].iloc[i]
                }
                
                # 添加到DataFrame
                new_row_df = pd.DataFrame([kline_row])
                if not new_row_df.empty:  # 确保新行不为空
                    kline_data = pd.concat([kline_data, new_row_df], ignore_index=True)
            
            # 保存到Excel文件
            kline_data.to_excel(excel_path, index=False)
            
            # 记录上一根K线的时间
            last_kline_time = datetime.datetime.fromtimestamp(klines.iloc[-1]['datetime'] / 1e9)
            
            print(f"\n已保存初始{len(klines)}条K线数据")
        else:
            print(f"警告: 合约 {rt_symbol} 没有获取到K线数据")
            failed_symbols.append((rt_symbol, "没有获取到K线数据"))
    
    except Exception as e:
        print(f"处理合约 {rt_symbol} 时发生错误: {e}")
        failed_symbols.append((rt_symbol, str(e)))
        continue  # 跳过当前合约，继续处理下一个

# 关闭接口
api.close()

# 打印处理结果统计
print("\n" + "="*50)
print("数据获取完成")
print("="*50)
print(f"总合约数: {len(symbol_list)}")
print(f"成功处理: {len(symbol_list) - len(failed_symbols)}")
print(f"处理失败: {len(failed_symbols)}")

# 如果有失败的品种，打印详细信息
if failed_symbols:
    print("\n处理失败的品种列表:")
    for idx, (symbol, error) in enumerate(failed_symbols, 1):
        print(f"{idx}. {symbol}: {error}")
