#!/usr/bin/env python3
"""
测试优化后的功能
"""
import os
import datetime
import pandas as pd

def test_html_directory():
    """测试HTML保存目录"""
    print("=== 测试HTML保存目录 ===")
    html_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\推送html"
    
    if os.path.exists(html_dir):
        print(f"✓ HTML目录已存在: {html_dir}")
        files = os.listdir(html_dir)
        print(f"✓ 目录中有 {len(files)} 个文件")
        if files:
            print(f"  最新文件: {sorted(files)[-1] if files else '无'}")
    else:
        print(f"✗ HTML目录不存在: {html_dir}")
        try:
            os.makedirs(html_dir)
            print(f"✓ 已创建HTML目录: {html_dir}")
        except Exception as e:
            print(f"✗ 创建HTML目录失败: {e}")
    
    return os.path.exists(html_dir)

def test_parallel_execution_concept():
    """测试并行执行概念"""
    print("\n=== 测试并行执行概念 ===")
    
    # 模拟原来的顺序执行时间
    sequential_time = 3 * 30 + 3 * 3 + 10  # 3个脚本各30分钟 + 3次等待各3秒 + 分析10分钟
    print(f"原顺序执行预计时间: {sequential_time} 分钟")
    
    # 模拟优化后的并行执行时间
    parallel_time = 30 + 2 + 10  # 并行执行30分钟 + 等待2秒 + 分析10分钟
    print(f"优化并行执行预计时间: {parallel_time} 分钟")
    
    time_saved = sequential_time - parallel_time
    print(f"✓ 预计节省时间: {time_saved} 分钟")
    print(f"✓ 时间优化率: {time_saved/sequential_time*100:.1f}%")
    
    return time_saved > 0

def test_b2_syntax():
    """测试b2.py语法"""
    print("\n=== 测试b2.py语法 ===")
    try:
        with open('b2.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'b2.py', 'exec')
        print("✓ b2.py语法检查通过")
        
        # 检查是否包含并行执行相关代码
        if 'ThreadPoolExecutor' in code:
            print("✓ 包含并行执行代码")
        else:
            print("✗ 缺少并行执行代码")
            
        if 'run_parallel_data_scripts' in code:
            print("✓ 包含并行数据脚本方法")
        else:
            print("✗ 缺少并行数据脚本方法")
            
        return True
    except Exception as e:
        print(f"✗ b2.py语法错误: {e}")
        return False

def test_b1_enhancements():
    """测试b1.py增强功能"""
    print("\n=== 测试b1.py增强功能 ===")
    try:
        with open('b1.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'b1.py', 'exec')
        print("✓ b1.py语法检查通过")
        
        # 检查新增功能
        enhancements = [
            ('save_html_report', 'HTML保存功能'),
            ('save_excel_records', 'Excel记录功能'),
            ('record_triggered_symbol', '品种记录功能'),
            ('triggered_records', '记录列表属性'),
            ('html_dir', 'HTML目录属性')
        ]
        
        all_present = True
        for feature, description in enhancements:
            if feature in code:
                print(f"✓ 包含{description}")
            else:
                print(f"✗ 缺少{description}")
                all_present = False
        
        return all_present
    except Exception as e:
        print(f"✗ b1.py语法错误: {e}")
        return False

def test_symbol_allocation():
    """测试合约分配"""
    print("\n=== 测试合约分配 ===")
    try:
        symbols_df = pd.read_excel('generated_symbols_9_2025.xlsx')
        total_symbols = len(symbols_df)
        print(f"总合约数: {total_symbols}")
        
        # 测试三个脚本的合约分配
        a_symbols = symbols_df.iloc[:25, 0].astype(str).tolist()  # 用户修改为25
        a1_symbols = symbols_df.iloc[26:52, 0].astype(str).tolist()
        a2_symbols = symbols_df.iloc[52:79, 0].astype(str).tolist()
        
        print(f"a.py: 第1-25个合约，共{len(a_symbols)}个")
        print(f"a_1.py: 第27-52个合约，共{len(a1_symbols)}个")
        print(f"a_2.py: 第53-79个合约，共{len(a2_symbols)}个")
        
        total_processed = len(a_symbols) + len(a1_symbols) + len(a2_symbols)
        print(f"总处理合约数: {total_processed}")
        
        # 检查覆盖情况
        coverage = total_processed / total_symbols * 100
        print(f"覆盖率: {coverage:.1f}%")
        
        if coverage >= 95:  # 允许一些容差
            print("✓ 合约分配覆盖率良好")
            return True
        else:
            print("✗ 合约分配覆盖率不足")
            return False
            
    except Exception as e:
        print(f"✗ 测试合约分配时发生错误: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试优化后的功能...")
    print("=" * 60)
    
    tests = [
        ("HTML保存目录", test_html_directory),
        ("并行执行概念", test_parallel_execution_concept),
        ("b2.py语法和功能", test_b2_syntax),
        ("b1.py增强功能", test_b1_enhancements),
        ("合约分配", test_symbol_allocation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20s}: {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！")
        print("📈 优化功能:")
        print("  • b2.py: 并行执行数据获取脚本，减少约60分钟延迟")
        print("  • b1.py: 自动保存HTML报告和Excel记录")
        print("  • 目录: 自动创建推送html目录")
        print("  • 合约: 合理分配79个合约到3个脚本")
    else:
        print(f"\n❌ {len(results)-passed} 个测试失败，请检查上述错误")

if __name__ == "__main__":
    main()
