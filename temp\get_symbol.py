import pandas as pd
import datetime
import os

def generate_contract_codes():
    generated_contracts = []

    # 1. Define years and month for generation
    target_year = 2025
    target_month = 10 # September

    # 2. Add static Index codes
    static_indices = [
        {"ContractID": "SSE.000016", "Exchange": "SSE", "Name": "上证50指数", "Type": "Index"},
        {"ContractID": "SSE.000300", "Exchange": "SSE", "Name": "沪深300指数", "Type": "Index"},
        {"ContractID": "SSE.000905", "Exchange": "SSE", "Name": "中证500指数", "Type": "Index"},
        {"ContractID": "SSE.000852", "Exchange": "SSE", "Name": "中证1000指数", "Type": "Index"},
    ]
    for idx in static_indices:
        generated_contracts.append(idx)

    # 3. Read and parse symbols.md
    symbols_md_path = 'symbols.md'
    parsed_symbols = []
    if not os.path.exists(symbols_md_path):
        print(f"Error: {symbols_md_path} not found.")
        # Create DataFrame from only static indices if md file is missing
        df_output = pd.DataFrame(generated_contracts)
        if not df_output.empty:
             df_output.to_excel(f"generated_symbols_{target_month}_2025.xlsx", index=False)
             print(f"Generated symbols (indices only) saved to generated_symbols_{target_month}_2025.xlsx")
        else:
             print("No symbols to save.")
        return

    try:
        with open(symbols_md_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) < 2:
            print(f"Warning: {symbols_md_path} is too short or has an unexpected format.")
        else:
            data_lines = lines[2:] 
            for line_num, line_content in enumerate(data_lines):
                line_content = line_content.strip()
                if not line_content:
                    continue
                parts = [part.strip() for part in line_content.split('|')]
                if len(parts) == 3:
                    parsed_symbols.append({
                        'symbol_raw': parts[0],
                        'exchange_short': parts[1],
                        'name_continuous': parts[2]
                    })
                else:
                    print(f"Warning: Skipping line {line_num + 2} in {symbols_md_path} due to unexpected format: {line_content}")
    except Exception as e:
        print(f"Error reading or parsing {symbols_md_path}: {e}")
    
    df_symbols = pd.DataFrame(parsed_symbols)

    # 4. Generate Future contract codes for September 2025
    exchange_map = {
        "dce": "DCE", "czce": "CZCE", "shfe": "SHFE",
        "cffex": "CFFEX", "ine": "INE", "gfex": "GFEX"
    }

    for _, row in df_symbols.iterrows():
        symbol_raw = row['symbol_raw']
        exchange_short = row['exchange_short']
        name_continuous = row['name_continuous']

        exchange_code = exchange_map.get(exchange_short.lower())
        if not exchange_code:
            print(f"Warning: Unknown exchange '{exchange_short}' for symbol '{symbol_raw}'. Skipping.")
            continue

        base_product_name = name_continuous.replace("连续", "")
        
        prefix_base = symbol_raw[:-1] if symbol_raw.endswith('0') else symbol_raw
        
        prefix = ""
        if exchange_code in ["DCE", "SHFE", "INE", "GFEX"]:
            prefix = prefix_base.lower()
        elif exchange_code in ["CZCE", "CFFEX"]:
            prefix = prefix_base.upper()
        else:
            prefix = prefix_base 

        yy = str(target_year)[-2:]
        mm = f"{target_month:02d}"
        y_digit = str(target_year)[-1]
        
        contract_id = ""
        descriptive_name_suffix = ""

        if exchange_code == "CZCE":
            suffix = f"{y_digit}{mm}"
            contract_id = f"{exchange_code}.{prefix}{suffix}"
            descriptive_name_suffix = suffix
        else:
            suffix = f"{yy}{mm}"
            contract_id = f"{exchange_code}.{prefix}{suffix}"
            descriptive_name_suffix = suffix
        
        descriptive_name = f"{base_product_name}{descriptive_name_suffix}"
        
        generated_contracts.append({
            "ContractID": contract_id,
            "Exchange": exchange_code,
            "Name": descriptive_name,
            "Type": "Future"
        })

    # 5. Save to Excel
    df_output = pd.DataFrame(generated_contracts)
    excel_filename = f"generated_symbols_{target_month}_2025.xlsx"
    if not df_output.empty:
        df_output.to_excel(excel_filename, index=False, columns=["ContractID", "Exchange", "Name", "Type"])
        print(f"Generated symbols for {target_month} 2025 saved to {excel_filename}")
    else:
        print("No symbols were generated or found to save.")

if __name__ == "__main__":
    generate_contract_codes()
