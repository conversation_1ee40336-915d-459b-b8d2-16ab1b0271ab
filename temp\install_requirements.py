#!/usr/bin/env python3
"""
安装定时调度器所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package_name):
    """
    安装指定的包
    """
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name):
    """
    检查包是否已安装
    """
    try:
        __import__(package_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装")
        return False

def main():
    """
    主函数
    """
    print("=" * 50)
    print("定时调度器依赖安装程序")
    print("=" * 50)
    
    # 需要安装的包列表
    required_packages = [
        "schedule",
    ]
    
    print("检查已安装的包...")
    all_installed = True
    
    for package in required_packages:
        if not check_package(package):
            all_installed = False
    
    if all_installed:
        print("\n所有依赖包都已安装！")
        return
    
    print("\n开始安装缺失的包...")
    
    for package in required_packages:
        if not check_package(package):
            install_package(package)
    
    print("\n安装完成！")
    print("现在可以运行: python b.py")

if __name__ == "__main__":
    main()
