# K线数据获取与分析定时调度器 (b2.py) 使用说明

## 概述

b2.py 是一个定时任务调度器，用于按照预设的时间表顺序运行数据获取脚本(a.py)和K线分析脚本(b1.py)，确保数据获取完成后再进行分析。现已支持**全品种监控**，不再限制为5个品种。

## 功能特点

- ✅ **全品种监控**：处理Excel文件中的全部品种，不再限制为5个
- ✅ 顺序执行：确保a.py完成后再运行b1.py
- ✅ 定时调度：支持多个时间点的自动执行
- ✅ 进程管理：自动处理进程启动、监控和终止
- ✅ 智能超时：针对全品种处理调整了超时时间
- ✅ 详细日志：完整记录执行过程和结果
- ✅ 状态监控：实时查看任务运行状态
- ✅ 测试功能：支持单独测试各个脚本

## 执行流程

```
定时触发 -> 运行a.py -> 等待完成 -> 运行b1.py -> 完成
```

### 详细步骤

1. **第1步：数据获取**
   - 运行 a.py 脚本
   - 超时时间：10分钟
   - 等待脚本完全执行完成

2. **第2步：数据分析**
   - 等待3秒确保数据文件写入完成
   - 运行 b1.py 脚本
   - 超时时间：5分钟

3. **结果记录**
   - 记录每个步骤的执行结果
   - 如果第1步失败，跳过第2步
   - 完整的日志记录

## 定时时间表（30分钟间隔）

### 交易时段
- **上午时段**: 09:00-11:30 (每30分钟)
- **下午时段**: 13:30-15:00 (每30分钟)
- **夜盘时段**: 21:00-23:00 (每30分钟)

### 具体时间点
```
09:00, 09:30, 10:00, 10:30, 11:00, 11:30
13:30, 14:00, 14:30, 15:00
21:00, 21:30, 22:00, 22:30, 23:00
```

### 时间配置说明
- **30分钟间隔**：与30分钟K线周期完美匹配
- **总计15个时间点**：覆盖主要交易时段
- **自动适配**：程序自动使用当日日期

## 使用方法

### 1. 启动调度器
```bash
# 启动定时调度器（主要用法）
python b2.py
```

### 2. 测试功能
```bash
# 测试完整任务序列
python b2.py test

# 单独测试数据获取脚本
python b2.py test-data

# 单独测试分析脚本
python b2.py test-analysis
```

### 3. 状态查看
```bash
# 查看当前状态
python b2.py status

# 查看调度信息
python b2.py info
```

### 4. 帮助信息
```bash
python b2.py help
```

## 日志文件

- **文件名**: `scheduler.log`
- **内容**: 详细的调度和执行日志
- **格式**: 时间戳 + 日志级别 + 消息

### 日志示例
```
2025-01-09 09:00:00 - INFO - 开始执行定时任务序列
2025-01-09 09:00:00 - INFO - 第1步：运行数据获取脚本
2025-01-09 09:00:00 - INFO - 正在启动 数据获取脚本(a.py): a.py
2025-01-09 09:00:05 - INFO - 数据获取脚本(a.py) 执行成功
2025-01-09 09:00:08 - INFO - 第2步：运行K线分析脚本
2025-01-09 09:00:08 - INFO - 正在启动 K线分析脚本(b1.py): b1.py
2025-01-09 09:00:15 - INFO - K线分析脚本(b1.py) 执行成功
2025-01-09 09:00:15 - INFO - 任务序列执行完成 - 全部成功
```

## 配置参数

### 脚本路径
```python
self.data_script = "a.py"      # 数据获取脚本
self.analysis_script = "b1.py"  # 分析脚本
```

### 超时设置（已针对全品种优化）
```python
# 数据获取脚本超时：30分钟（全品种处理需要更多时间）
data_success = self.run_single_script(self.data_script, "数据获取脚本(a.py)", timeout=1800)

# 分析脚本超时：10分钟（全品种分析需要更多时间）
analysis_success = self.run_single_script(self.analysis_script, "K线分析脚本(b1.py)", timeout=600)
```

## 全品种监控说明

### 重要变更
- **之前版本**：只处理Excel文件中的前5个品种
- **当前版本**：处理Excel文件中的全部品种

### 代码变更
在a.py中的关键修改：
```python
# 之前：只获取前5个合约
symbol_list = symbols_df.iloc[:5, 0].astype(str).tolist()

# 现在：获取全部合约
symbol_list = symbols_df.iloc[:, 0].astype(str).tolist()
```

### 性能影响
- **数据获取时间**：从约2-3分钟增加到可能10-30分钟（取决于品种数量）
- **分析时间**：从约30秒增加到可能2-10分钟
- **超时设置**：已相应调整为30分钟和10分钟

### 监控范围
- 自动读取`generated_symbols_9_2025.xlsx`中的全部品种
- 支持任意数量的品种（理论上无限制）
- 每个品种独立处理，互不影响

## 错误处理

### 1. 脚本文件不存在
- 检查a.py和b1.py是否在当前目录
- 确认文件路径配置正确

### 2. 脚本执行失败
- 查看scheduler.log了解详细错误
- 使用测试命令单独验证脚本

### 3. 超时问题
- 检查脚本是否有死循环
- 适当调整超时时间

### 4. 进程冲突
- 调度器会自动终止之前的进程
- 避免手动运行脚本与调度器冲突

## 高级用法

### 修改调度时间
编辑 `schedule_times` 列表：
```python
self.schedule_times = [
    "09:00:00",
    "10:00:00",
    # 添加或修改时间点
]
```

### 修改脚本路径
```python
self.data_script = "path/to/your/data_script.py"
self.analysis_script = "path/to/your/analysis_script.py"
```

### 修改超时时间
在 `run_task_sequence` 方法中调整timeout参数。

## 监控和维护

### 1. 日常监控
- 定期查看 `scheduler.log` 文件
- 使用 `python b2.py status` 检查状态
- 监控磁盘空间（日志文件会持续增长）

### 2. 故障排除
1. 检查脚本文件是否存在
2. 验证脚本是否可以独立运行
3. 查看日志文件了解详细错误
4. 使用测试命令验证功能

### 3. 性能优化
- 根据实际需要调整超时时间
- 定期清理旧的日志文件
- 监控系统资源使用情况

## 依赖要求

```bash
pip install schedule
```

## 注意事项

1. **确保脚本可执行**：a.py和b1.py必须能够独立运行
2. **避免并发运行**：不要同时运行多个调度器实例
3. **监控日志大小**：定期清理或轮转日志文件
4. **系统资源**：确保系统有足够资源运行脚本
5. **网络连接**：确保数据获取脚本的网络连接稳定

## 快速开始

1. **准备脚本**：确保a.py和b1.py在当前目录
2. **安装依赖**：`pip install schedule`
3. **测试功能**：`python b2.py test`
4. **启动调度器**：`python b2.py`
5. **监控运行**：查看scheduler.log文件

## 故障排除指南

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 脚本不存在错误 | a.py或b1.py文件缺失 | 检查文件是否在当前目录 |
| 执行超时 | 脚本运行时间过长 | 检查脚本逻辑，调整超时时间 |
| 权限错误 | 文件权限不足 | 检查文件执行权限 |
| 导入错误 | 缺少依赖库 | 安装所需的Python包 |
| 网络错误 | 网络连接问题 | 检查网络连接和防火墙设置 |
