import smtplib  # 发邮件的模块
from email.mime.text import MIMEText  # 定义邮件内容
from email.header import Header
import logging


def send_mail(mail_content=None, subject=None, receives=None):
    """
    发送邮件函数

    参数:
    mail_content: 邮件内容，支持HTML格式
    subject: 邮件主题
    receives: 接收邮箱列表
    """
    # 默认值设置
    if mail_content is None:
        mail_content = "邮件内容：1234567890"
    if subject is None:
        subject = "最新测试报告"
    if receives is None:
        receives = ['<EMAIL>']

    # 发送邮箱服务器
    smtp_server = 'smtp.163.com'

    # 发送邮箱用户名和密码
    user = '<EMAIL>'
    password = 'a4719197'  # 设置的邮件服务独立密码

    # 发送和接收邮箱
    sender = '<EMAIL>'

    # 构建发送和接收信息
    msg = MIMEText(mail_content, 'html', 'utf-8')
    msg['subject'] = Header(subject, 'utf-8')
    msg['From'] = sender
    msg['To'] = ','.join(receives)

    # SSl协议端口号要使用465
    smtp = smtplib.SMTP_SSL(smtp_server, 465)

    # HELO向服务器标识用户的身份
    smtp.helo(smtp_server)

    # EHLO 服务器返回结果确认
    smtp.ehlo(smtp_server)

    # 登录邮箱服务器用户名和密码
    smtp.login(user, password)

    logging.info("Start send Email....")

    smtp.sendmail(sender, receives, msg.as_string())
    smtp.quit()
    logging.info("Send End!")


if __name__ == '__main__':
    send_mail()
