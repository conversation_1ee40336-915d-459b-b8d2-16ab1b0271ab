#!/usr/bin/env python3
"""
测试合约分配
"""
import pandas as pd

def test_symbol_allocation():
    """测试三个脚本的合约分配"""
    print("=== 合约分配测试 ===")
    
    # 读取Excel文件
    symbols_df = pd.read_excel('generated_symbols_9_2025.xlsx')
    total_symbols = len(symbols_df)
    print(f"总合约数: {total_symbols}")
    
    print("\n=== 各脚本分配情况 ===")
    
    # a.py: 第1-26个合约
    a_symbols = symbols_df.iloc[:26, 0].astype(str).tolist()
    print(f"a.py: 第1-26个合约，共{len(a_symbols)}个")
    print(f"  首个: {a_symbols[0]}")
    print(f"  末个: {a_symbols[-1]}")
    
    # a_1.py: 第27-52个合约
    a1_symbols = symbols_df.iloc[26:52, 0].astype(str).tolist()
    print(f"a_1.py: 第27-52个合约，共{len(a1_symbols)}个")
    print(f"  首个: {a1_symbols[0]}")
    print(f"  末个: {a1_symbols[-1]}")
    
    # a_2.py: 第53-79个合约
    a2_symbols = symbols_df.iloc[52:79, 0].astype(str).tolist()
    print(f"a_2.py: 第53-79个合约，共{len(a2_symbols)}个")
    print(f"  首个: {a2_symbols[0]}")
    print(f"  末个: {a2_symbols[-1]}")
    
    # 验证覆盖情况
    total_processed = len(a_symbols) + len(a1_symbols) + len(a2_symbols)
    print(f"\n=== 覆盖验证 ===")
    print(f"总合约数: {total_symbols}")
    print(f"处理合约数: {total_processed}")
    print(f"覆盖率: {total_processed/total_symbols*100:.1f}%")
    
    # 检查是否有重复
    all_processed = a_symbols + a1_symbols + a2_symbols
    unique_processed = list(set(all_processed))
    print(f"去重后数量: {len(unique_processed)}")
    
    has_duplicate = len(all_processed) != len(unique_processed)
    print(f"是否有重复: {'是' if has_duplicate else '否'}")
    
    # 检查是否完全覆盖
    is_complete = total_processed == total_symbols and not has_duplicate
    print(f"是否完全覆盖: {'是' if is_complete else '否'}")
    
    if is_complete:
        print("\n✅ 合约分配正确！三个脚本将完全覆盖所有79个合约，无重复无遗漏。")
    else:
        print("\n❌ 合约分配有问题，需要调整。")
    
    return is_complete

if __name__ == "__main__":
    test_symbol_allocation()
