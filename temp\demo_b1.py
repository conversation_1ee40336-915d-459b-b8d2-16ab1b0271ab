#!/usr/bin/env python3
"""
b1.py K线分析器演示脚本
展示如何使用包含ATR和BOLL指标的K线数据进行异常检测
"""

import os
import sys
import pandas as pd
import numpy as np
import datetime
from b1 import KLineAnalyzer

def create_demo_data():
    """
    创建演示用的K线数据（包含ATR和BOLL指标）
    """
    print("创建演示数据...")
    
    # 使用当日日期
    today = datetime.datetime.now().strftime('%Y%m%d')
    demo_dir = f"demo_data/{today}"
    os.makedirs(demo_dir, exist_ok=True)
    
    # 生成演示K线数据
    np.random.seed(42)
    
    symbols = ["DCE.v2509", "SSE.000016", "SHFE.cu2509"]
    
    for symbol in symbols:
        print(f"生成 {symbol} 的数据...")
        
        # 生成50根K线数据
        n_bars = 50
        base_price = 100.0
        
        # 生成价格数据
        price_changes = np.random.normal(0, 0.3, n_bars)
        
        # 添加一些异常变化
        if symbol == "DCE.v2509":
            price_changes[15] = 2.5  # 大涨，触发涨跌幅条件
            price_changes[25] = -2.0  # 大跌
        elif symbol == "SSE.000016":
            price_changes[20] = 1.8  # 中等涨幅
        elif symbol == "SHFE.cu2509":
            price_changes[10] = 1.2  # 小涨
            price_changes[30] = -1.5  # 中跌
        
        # 生成OHLC数据
        opens, highs, lows, closes, volumes = [], [], [], [], []
        current_price = base_price
        
        for i in range(n_bars):
            open_price = current_price
            change = price_changes[i]
            close_price = open_price * (1 + change / 100)
            
            if change > 0:
                high_price = close_price * (1 + np.random.uniform(0, 0.3) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.2) / 100)
            else:
                high_price = open_price * (1 + np.random.uniform(0, 0.2) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.3) / 100)
            
            volume = np.random.randint(1000, 10000)
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 计算ATR指标
        atr_tr_values = []
        atr_values = []
        
        for i in range(n_bars):
            if i == 0:
                atr_tr = highs[i] - lows[i]
                atr = atr_tr
            else:
                tr1 = highs[i] - lows[i]
                tr2 = abs(highs[i] - closes[i-1])
                tr3 = abs(lows[i] - closes[i-1])
                atr_tr = max(tr1, tr2, tr3)
                
                # 14周期移动平均
                if i < 14:
                    atr = sum(atr_tr_values + [atr_tr]) / (i + 1)
                else:
                    atr = sum(atr_tr_values[-13:] + [atr_tr]) / 14
            
            atr_tr_values.append(round(atr_tr, 4))
            atr_values.append(round(atr, 4))
        
        # 计算BOLL指标
        boll_upper, boll_middle, boll_lower = [], [], []
        
        for i in range(n_bars):
            if i < 20:
                middle = closes[i]
                std = 0.5
            else:
                period_closes = closes[max(0, i-19):i+1]
                middle = sum(period_closes) / len(period_closes)
                variance = sum((x - middle) ** 2 for x in period_closes) / len(period_closes)
                std = variance ** 0.5
            
            # 为了演示BOLL突破，人为调整某些数据
            if symbol == "DCE.v2509" and i == 35:
                # 让价格接近上轨
                upper = middle + 2 * std
                lower = middle - 2 * std
                closes[i] = upper * 0.98  # 接近上轨
            elif symbol == "SSE.000016" and i == 40:
                # 让价格接近下轨
                upper = middle + 2 * std
                lower = middle - 2 * std
                closes[i] = lower * 1.02  # 接近下轨
            else:
                upper = middle + 2 * std
                lower = middle - 2 * std
            
            boll_upper.append(round(upper, 2))
            boll_middle.append(round(middle, 2))
            boll_lower.append(round(lower, 2))
        
        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': pd.date_range('2025-01-09 09:00:00', periods=n_bars, freq='15min'),
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'atr_tr': atr_tr_values,
            'atr': atr_values,
            'boll_upper': boll_upper,
            'boll_middle': boll_middle,
            'boll_lower': boll_lower
        })
        
        # 保存到Excel文件
        file_name = f"{symbol.replace('.', '_')}_kline_indicators.xlsx"
        file_path = os.path.join(demo_dir, file_name)
        df.to_excel(file_path, index=False)
        
        print(f"  文件: {file_path}")
        print(f"  数据行数: {len(df)}")
        print(f"  价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"  最大涨幅: {((df['close'] - df['open']) / df['open'] * 100).max():.2f}%")
        print(f"  最大跌幅: {((df['close'] - df['open']) / df['open'] * 100).min():.2f}%")
        print(f"  ATR范围: {df['atr'].min():.4f} - {df['atr'].max():.4f}")
    
    print(f"\n演示数据创建完成，保存在: {demo_dir}")
    return demo_dir

def run_demo():
    """
    运行演示
    """
    print("=" * 60)
    print("b1.py 倒数第二根K线分析器演示（仅ATR+BOLL）")
    print("=" * 60)
    
    # 创建演示数据
    demo_dir = create_demo_data()
    
    # 创建分析器
    analyzer = KLineAnalyzer(data_dir="demo_data")
    
    # 设置较低的阈值以便演示
    analyzer.price_change_threshold = 1.0  # 1%
    analyzer.atr_multiplier = 1.5  # 1.5倍ATR
    analyzer.boll_threshold = 0.9   # 90%位置
    
    print(f"\n演示配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  分析日期: {analyzer.target_date}")
    print(f"  分析范围: 仅倒数第二根K线")
    print(f"  触发条件: 仅ATR+BOLL（涨跌幅已禁用）")
    print(f"  ATR倍数阈值: {analyzer.atr_multiplier}")
    print(f"  BOLL突破阈值: {analyzer.boll_threshold}")

    # 运行分析（不发送邮件）
    print(f"\n开始分析倒数第二根K线...")
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n演示结果:")
    print(f"  异常品种总数: {result['total_abnormal']}")
    print(f"  上涨异常: {result['up_count']} 个品种")
    print(f"  下跌异常: {result['down_count']} 个品种")
    print(f"  ATR超标: {result['atr_exceeded_count']} 个品种")
    print(f"  BOLL突破: {result['boll_breakthrough_count']} 个品种")
    print(f"  涨跌幅超标: {result['price_change_count']} 个品种 (应为0)")
    print(f"  涉及品种: {result['symbols']}")
    
    # 显示详细信息
    if result['abnormal_klines']:
        print(f"\n异常K线详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     ATR比值: {kline['body_atr_ratio']:.2f}")
            print(f"     BOLL位置: {kline['boll_position']:.2f}")
            print(f"     触发原因: {'; '.join(kline['trigger_reason'])}")
    
    # 清理演示数据
    import shutil
    if os.path.exists("demo_data"):
        shutil.rmtree("demo_data")
        print(f"\n演示数据已清理")
    
    print(f"\n演示完成！")

if __name__ == "__main__":
    run_demo()
