#!/usr/bin/env python3
"""
测试修复后的代码
"""
import pandas as pd
import os
import sys

def test_excel_reading():
    """测试Excel文件读取"""
    print("=== 测试Excel文件读取 ===")
    try:
        symbols_df = pd.read_excel("generated_symbols_9_2025.xlsx")
        if not symbols_df.empty and symbols_df.shape[1] > 0:
            # 获取全部合约代码（移除25个限制）
            symbol_list = symbols_df.iloc[:, 0].astype(str).tolist()
            print(f"✓ 成功读取Excel文件")
            print(f"✓ 总合约数: {len(symbol_list)}")
            print(f"✓ 前5个合约: {symbol_list[:5]}")
            print(f"✓ 后5个合约: {symbol_list[-5:]}")
            return True, len(symbol_list)
        else:
            print("✗ Excel文件为空或没有列")
            return False, 0
    except FileNotFoundError:
        print("✗ Excel文件未找到")
        return False, 0
    except Exception as e:
        print(f"✗ 读取Excel文件时发生错误: {e}")
        return False, 0

def test_b2_syntax():
    """测试b2.py语法"""
    print("\n=== 测试b2.py语法 ===")
    try:
        # 尝试编译b2.py
        with open('b2.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'b2.py', 'exec')
        print("✓ b2.py语法检查通过")
        return True
    except SyntaxError as e:
        print(f"✗ b2.py语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 检查b2.py时发生错误: {e}")
        return False

def test_a_syntax():
    """测试a.py语法"""
    print("\n=== 测试a.py语法 ===")
    try:
        # 尝试编译a.py
        with open('a.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'a.py', 'exec')
        print("✓ a.py语法检查通过")
        return True
    except SyntaxError as e:
        print(f"✗ a.py语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 检查a.py时发生错误: {e}")
        return False

def test_schedule_import():
    """测试schedule库导入"""
    print("\n=== 测试schedule库导入 ===")
    try:
        import schedule
        print("✓ schedule库导入成功")
        return True
    except ImportError:
        print("✗ schedule库未安装")
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的代码...")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 测试Excel读取
    excel_ok, symbol_count = test_excel_reading()
    results.append(("Excel文件读取", excel_ok))
    
    # 测试语法
    b2_ok = test_b2_syntax()
    results.append(("b2.py语法", b2_ok))
    
    a_ok = test_a_syntax()
    results.append(("a.py语法", a_ok))
    
    # 测试依赖
    schedule_ok = test_schedule_import()
    results.append(("schedule库", schedule_ok))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name:20s}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！")
        print(f"📊 a.py现在可以处理全部{symbol_count}个合约（之前只有25个）")
        print("🔧 b2.py的语法错误已修复")
        print("📦 所需依赖已安装")
    else:
        print("❌ 部分测试失败，请检查上述错误")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
