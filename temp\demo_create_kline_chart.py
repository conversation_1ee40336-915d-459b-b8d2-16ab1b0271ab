#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示如何调用create_kline_chart函数绘制K线图
"""

import os
import pandas as pd
import base64
from b1_macd_1 import KLineAnalyzer

def demo_create_kline_chart():
    """
    演示create_kline_chart函数的使用方法
    """
    print("=" * 60)
    print("演示create_kline_chart函数的使用")
    print("=" * 60)
    
    # 1. 创建分析器实例
    analyzer = KLineAnalyzer(data_dir="data", kline_period="30min")
    print(f"✓ 创建分析器实例")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  K线周期: {analyzer.kline_period}")
    print(f"  图表历史K线数量: {analyzer.chart_history_bars}")
    
    # 2. 读取测试数据
    data_files = [f for f in os.listdir(analyzer.target_path) if f.endswith('.xlsx')]
    if not data_files:
        print("❌ 未找到数据文件")
        return
    
    test_file = data_files[0]
    file_path = os.path.join(analyzer.target_path, test_file)
    df = pd.read_excel(file_path)
    symbol = test_file.replace('_kline_data.xlsx', '').replace('_', '.')
    
    print(f"\n✓ 读取测试数据")
    print(f"  文件: {test_file}")
    print(f"  品种: {symbol}")
    print(f"  K线数量: {len(df)}")
    print(f"  数据列: {list(df.columns)}")
    
    # 3. 基本调用示例
    print(f"\n📊 示例1: 基本调用（只标记入场点）")
    chart_base64_1 = analyzer.create_kline_chart(
        df=df,                    # K线数据DataFrame
        symbol=symbol,            # 品种代码
        abnormal_index=-2         # 倒数第2根K线作为入场点
    )
    
    if chart_base64_1:
        print(f"✓ 图表生成成功")
        print(f"  Base64长度: {len(chart_base64_1)} 字符")
        print(f"  预估大小: {len(chart_base64_1) * 3 // 4 // 1024:.1f} KB")
        
        # 保存为HTML文件
        save_chart_as_html(chart_base64_1, symbol, "基本调用示例", "demo_basic_chart.html")
    else:
        print("❌ 图表生成失败")
    
    # 4. 带峰值标记的调用示例
    print(f"\n📊 示例2: 带峰值标记的调用")
    chart_base64_2 = analyzer.create_kline_chart(
        df=df,                    # K线数据DataFrame
        symbol=symbol,            # 品种代码
        abnormal_index=-5,        # 倒数第5根K线作为入场点
        peak_indices=[-8, -25]    # 峰值位置：倒数第8根和第25根K线
    )
    
    if chart_base64_2:
        print(f"✓ 图表生成成功（含峰值标记）")
        print(f"  Base64长度: {len(chart_base64_2)} 字符")
        print(f"  预估大小: {len(chart_base64_2) * 3 // 4 // 1024:.1f} KB")
        
        # 保存为HTML文件
        save_chart_as_html(chart_base64_2, symbol, "带峰值标记示例", "demo_peaks_chart.html")
    else:
        print("❌ 图表生成失败")
    
    # 5. 多种参数组合示例
    print(f"\n📊 示例3: 多种参数组合")
    
    test_cases = [
        {
            "name": "近期入场点",
            "abnormal_index": -1,
            "peak_indices": None,
            "filename": "demo_recent_entry.html"
        },
        {
            "name": "中期入场点",
            "abnormal_index": -10,
            "peak_indices": [-5, -15],
            "filename": "demo_medium_entry.html"
        },
        {
            "name": "远期入场点",
            "abnormal_index": -20,
            "peak_indices": [-10, -30],
            "filename": "demo_far_entry.html"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n  测试{i}: {case['name']}")
        chart_base64 = analyzer.create_kline_chart(
            df=df,
            symbol=symbol,
            abnormal_index=case['abnormal_index'],
            peak_indices=case['peak_indices']
        )
        
        if chart_base64:
            print(f"    ✓ 生成成功，大小: {len(chart_base64) * 3 // 4 // 1024:.1f} KB")
            save_chart_as_html(chart_base64, symbol, case['name'], case['filename'])
        else:
            print(f"    ❌ 生成失败")
    
    # 6. 参数说明
    print(f"\n📖 参数说明:")
    print(f"  df: pandas DataFrame，包含K线数据")
    print(f"      必需列: open, high, low, close")
    print(f"      可选列: datetime, volume, boll_upper, boll_middle, boll_lower, macd_histogram")
    print(f"  symbol: 字符串，品种代码（用于图表标题）")
    print(f"  abnormal_index: 整数，入场点位置（负数，从末尾开始计算）")
    print(f"      -1: 最后一根K线")
    print(f"      -2: 倒数第二根K线（默认）")
    print(f"      -5: 倒数第五根K线")
    print(f"  peak_indices: 列表，MACD峰值位置（可选）")
    print(f"      [current_peak_idx, prev_peak_idx]")
    print(f"      例: [-8, -25] 表示倒数第8根和第25根K线")
    
    # 7. 图表特性说明
    print(f"\n🎨 图表特性:")
    print(f"  上方子图: K线图 + BOLL带")
    print(f"    - 红色K线: 阳线（收盘价 >= 开盘价）")
    print(f"    - 绿色K线: 阴线（收盘价 < 开盘价）")
    print(f"    - 蓝色线: BOLL带（上轨、中轨、下轨）")
    print(f"    - 黄色边框: 入场点标记")
    print(f"    - 蓝色虚线边框: 峰值1标记（最近的峰值）")
    print(f"    - 绿色虚线边框: 峰值2标记（较早的峰值）")
    print(f"  下方子图: MACD柱状图")
    print(f"    - 红色柱: MACD柱 >= 0")
    print(f"    - 绿色柱: MACD柱 < 0")
    print(f"    - 黄色柱: 入场点对应的MACD柱")
    print(f"    - 蓝色柱: 峰值1对应的MACD柱")
    print(f"    - 绿色柱: 峰值2对应的MACD柱")
    
    print(f"\n✅ 演示完成！生成的HTML文件可以在浏览器中查看。")

def save_chart_as_html(chart_base64, symbol, description, filename):
    """
    将图表保存为HTML文件
    """
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{symbol} - {description}</title>
    <style>
        body {{ 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }}
        .container {{ 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{ 
            text-align: center; 
            margin-bottom: 20px; 
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 5px;
        }}
        .chart {{ 
            text-align: center; 
            margin: 20px 0; 
        }}
        .chart img {{ 
            max-width: 100%; 
            height: auto; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .info {{ 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }}
        .legend {{ 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 10px; 
            margin: 20px 0;
        }}
        .legend-item {{ 
            background: #fff; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 3px;
        }}
        .color-box {{ 
            display: inline-block; 
            width: 20px; 
            height: 15px; 
            margin-right: 8px; 
            border: 1px solid #ccc;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{symbol} K线图表</h1>
            <h3>{description}</h3>
        </div>
        
        <div class="info">
            <h3>📊 图表说明</h3>
            <p>这是使用 <code>create_kline_chart</code> 函数生成的K线图表，包含K线图、BOLL带、MACD柱状图和标记点。</p>
        </div>
        
        <div class="chart">
            <img src="data:image/png;base64,{chart_base64}" alt="{symbol} K线图表">
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <h4>K线图标记</h4>
                <p><span class="color-box" style="background: red;"></span>阳线（红色）</p>
                <p><span class="color-box" style="background: green;"></span>阴线（绿色）</p>
                <p><span class="color-box" style="background: yellow; border: 3px solid orange;"></span>入场点（黄色边框）</p>
                <p><span class="color-box" style="border: 2px dashed blue;"></span>峰值1（蓝色虚线）</p>
                <p><span class="color-box" style="border: 2px dashed green;"></span>峰值2（绿色虚线）</p>
            </div>
            
            <div class="legend-item">
                <h4>BOLL带</h4>
                <p><span class="color-box" style="background: blue; opacity: 0.7;"></span>BOLL上轨（蓝色虚线）</p>
                <p><span class="color-box" style="background: blue;"></span>BOLL中轨（蓝色实线）</p>
                <p><span class="color-box" style="background: blue; opacity: 0.7;"></span>BOLL下轨（蓝色虚线）</p>
                <p><span class="color-box" style="background: blue; opacity: 0.1;"></span>BOLL带区域（蓝色填充）</p>
            </div>
            
            <div class="legend-item">
                <h4>MACD柱状图</h4>
                <p><span class="color-box" style="background: red; opacity: 0.7;"></span>正值柱（红色）</p>
                <p><span class="color-box" style="background: green; opacity: 0.7;"></span>负值柱（绿色）</p>
                <p><span class="color-box" style="background: yellow; border: 2px solid orange;"></span>入场点柱（黄色）</p>
                <p><span class="color-box" style="background: blue; opacity: 0.7;"></span>峰值1柱（蓝色）</p>
                <p><span class="color-box" style="background: green; opacity: 0.7;"></span>峰值2柱（绿色）</p>
            </div>
        </div>
        
        <div class="info">
            <h3>🔧 函数调用参数</h3>
            <p><strong>品种代码:</strong> {symbol}</p>
            <p><strong>描述:</strong> {description}</p>
            <p><strong>生成时间:</strong> {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
"""
    
    with open(filename, "w", encoding="utf-8") as f:
        f.write(html_content)
    print(f"    💾 已保存到: {filename}")

def main():
    """
    主函数
    """
    try:
        demo_create_kline_chart()
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
