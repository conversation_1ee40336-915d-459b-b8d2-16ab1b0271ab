from tqsdk import TqApi, TqAuth
import datetime

## 推送实时k线数据
api = TqApi(auth=TqAuth("a_1325", "Az4719197"))

#rt_symbol = "SHFE.cu2509"
rt_symbol = "CZCE.AP510"
T_period = 60*2  # 1秒为单位
data_len = 100

klines = api.get_kline_serial(rt_symbol, T_period, data_length=data_len)

while api.wait_update():
    if api.is_changing(klines.iloc[-1], "datetime"):    # 判定最后一根K线的时间是否有变化
        klines = api.get_kline_serial(rt_symbol, T_period, data_length=data_len)
        latest_kline = klines.iloc[-2]
        # 转换datetime为可读格式
        readable_time = datetime.datetime.fromtimestamp(latest_kline['datetime'] / 1e9).strftime('%Y-%m-%d %H:%M:%S')
        print(f"时间: {readable_time}")
        print(f"开盘价: {latest_kline['open']}")
        print(f"最高价: {latest_kline['high']}")
        print(f"最低价: {latest_kline['low']}")
        print(f"收盘价: {latest_kline['close']}")
        print(f"成交量: {latest_kline['volume']}")                     