#!/usr/bin/env python3
"""
测试b1.py K线分析器功能
"""

import os
import sys
import pandas as pd
import numpy as np
from b1 import KLineAnalyzer

def create_test_data():
    """
    创建测试用的K线数据
    """
    print("创建测试数据...")
    
    # 创建测试目录
    test_dir = "test_data/20250603"
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成测试K线数据
    np.random.seed(42)  # 固定随机种子，确保结果可重现
    
    symbols = ["DCE.v2509", "SSE.000016", "SSE.000300"]
    
    for symbol in symbols:
        # 生成50根K线数据
        n_bars = 50
        
        # 基础价格
        base_price = 100.0
        
        # 生成价格数据
        price_changes = np.random.normal(0, 0.5, n_bars)  # 正态分布的价格变化
        
        # 添加几个异常大的变化
        if symbol == "DCE.v2509":
            price_changes[10] = 3.0  # 大涨
            price_changes[20] = -2.5  # 大跌
        elif symbol == "SSE.000016":
            price_changes[15] = 2.2  # 大涨
        
        opens = []
        highs = []
        lows = []
        closes = []
        volumes = []
        
        current_price = base_price
        
        for i in range(n_bars):
            open_price = current_price
            change = price_changes[i]
            close_price = open_price * (1 + change / 100)
            
            # 生成高低价
            if change > 0:  # 上涨
                high_price = close_price * (1 + np.random.uniform(0, 0.5) / 100)
                low_price = open_price * (1 - np.random.uniform(0, 0.3) / 100)
            else:  # 下跌
                high_price = open_price * (1 + np.random.uniform(0, 0.3) / 100)
                low_price = close_price * (1 - np.random.uniform(0, 0.5) / 100)
            
            volume = np.random.randint(1000, 10000)
            
            opens.append(round(open_price, 2))
            highs.append(round(high_price, 2))
            lows.append(round(low_price, 2))
            closes.append(round(close_price, 2))
            volumes.append(volume)
            
            current_price = close_price
        
        # 计算ATR指标
        atr_values = []
        atr_tr_values = []
        for i in range(n_bars):
            if i == 0:
                atr_tr = highs[i] - lows[i]
                atr = atr_tr
            else:
                tr1 = highs[i] - lows[i]
                tr2 = abs(highs[i] - closes[i-1])
                tr3 = abs(lows[i] - closes[i-1])
                atr_tr = max(tr1, tr2, tr3)

                # 简单移动平均
                if i < 14:
                    atr = sum(atr_tr_values + [atr_tr]) / (i + 1)
                else:
                    atr = sum(atr_tr_values[-13:] + [atr_tr]) / 14

            atr_tr_values.append(atr_tr)
            atr_values.append(atr)

        # 计算BOLL指标
        boll_upper = []
        boll_middle = []
        boll_lower = []

        for i in range(n_bars):
            if i < 20:
                # 数据不足，使用当前价格
                middle = closes[i]
                std = 0.5
            else:
                # 20周期移动平均和标准差
                period_closes = closes[max(0, i-19):i+1]
                middle = sum(period_closes) / len(period_closes)
                variance = sum((x - middle) ** 2 for x in period_closes) / len(period_closes)
                std = variance ** 0.5

            upper = middle + 2 * std
            lower = middle - 2 * std

            boll_upper.append(upper)
            boll_middle.append(middle)
            boll_lower.append(lower)

        # 创建DataFrame
        df = pd.DataFrame({
            'datetime': pd.date_range('2025-06-03 09:00:00', periods=n_bars, freq='15min'),
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'atr_tr': atr_tr_values,
            'atr': atr_values,
            'boll_upper': boll_upper,
            'boll_middle': boll_middle,
            'boll_lower': boll_lower
        })
        
        # 保存到Excel文件
        file_name = f"{symbol.replace('.', '_')}_kline_data.xlsx"
        file_path = os.path.join(test_dir, file_name)
        df.to_excel(file_path, index=False)
        
        print(f"创建测试文件: {file_path}")
        print(f"  数据行数: {len(df)}")
        print(f"  价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"  最大涨幅: {((df['close'] - df['open']) / df['open'] * 100).max():.2f}%")
        print(f"  最大跌幅: {((df['close'] - df['open']) / df['open'] * 100).min():.2f}%")
    
    print(f"测试数据创建完成，保存在: {test_dir}")
    return test_dir

def test_analyzer():
    """
    测试分析器功能
    """
    print("\n" + "=" * 50)
    print("测试K线分析器")
    print("=" * 50)
    
    # 创建测试数据
    test_dir = create_test_data()
    
    # 创建分析器
    analyzer = KLineAnalyzer(
        data_dir="test_data",
        target_date="20250603"
    )
    
    # 设置较低的阈值以便测试
    analyzer.price_change_threshold = 1.0  # 1%
    analyzer.atr_multiplier = 1.5  # 1.5倍ATR
    
    print(f"\n测试配置:")
    print(f"  数据目录: {analyzer.target_path}")
    print(f"  涨跌幅阈值: {analyzer.price_change_threshold}%")
    print(f"  ATR倍数阈值: {analyzer.atr_multiplier}")
    
    # 运行分析（不发送邮件）
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n测试结果:")
    print(f"  异常K线总数: {result['total_abnormal']}")
    print(f"  上涨异常: {result['up_count']}")
    print(f"  下跌异常: {result['down_count']}")
    print(f"  涉及品种: {result['symbols']}")
    
    # 显示详细的异常K线信息
    if result['abnormal_klines']:
        print(f"\n异常K线详情:")
        for i, kline in enumerate(result['abnormal_klines'], 1):
            print(f"  {i}. {kline['symbol']} - {kline['change_type']} {kline['price_change']:+.2f}%")
            print(f"     ATR比值: {kline['body_atr_ratio']:.2f}, 触发原因: {'; '.join(kline['trigger_reason'])}")
    
    # 清理测试数据
    import shutil
    if os.path.exists("test_data"):
        shutil.rmtree("test_data")
        print(f"\n测试数据已清理")
    
    return result

def test_real_data():
    """
    测试真实数据
    """
    print("\n" + "=" * 50)
    print("测试真实数据")
    print("=" * 50)
    
    # 检查真实数据目录是否存在
    real_data_path = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data\20250603"
    
    if not os.path.exists(real_data_path):
        print(f"真实数据目录不存在: {real_data_path}")
        print("跳过真实数据测试")
        return
    
    # 列出可用文件
    files = [f for f in os.listdir(real_data_path) if f.endswith('.xlsx') and not f.startswith('~$')]
    print(f"找到 {len(files)} 个数据文件:")
    for file in files:
        print(f"  - {file}")
    
    if not files:
        print("没有找到可用的数据文件")
        return
    
    # 创建分析器
    analyzer = KLineAnalyzer(target_date="20250603")
    
    print(f"\n分析配置:")
    print(f"  ATR周期: {analyzer.atr_period}")
    print(f"  ATR倍数阈值: {analyzer.atr_multiplier}")
    print(f"  涨跌幅阈值: {analyzer.price_change_threshold}%")
    
    # 运行分析（不发送邮件）
    result = analyzer.run_analysis(send_email=False)
    
    print(f"\n真实数据分析结果:")
    print(f"  异常K线总数: {result['total_abnormal']}")
    print(f"  上涨异常: {result['up_count']}")
    print(f"  下跌异常: {result['down_count']}")
    print(f"  涉及品种: {len(result['symbols'])}")
    
    if result['symbols']:
        print(f"  异常品种: {', '.join(result['symbols'])}")

def main():
    """
    主测试函数
    """
    print("=" * 60)
    print("b1.py K线分析器测试程序")
    print("=" * 60)
    
    try:
        # 测试模拟数据
        test_result = test_analyzer()
        
        # 测试真实数据
        test_real_data()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
