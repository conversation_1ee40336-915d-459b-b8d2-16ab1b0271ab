import schedule
import time
import datetime
import subprocess
import os
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class TaskScheduler:
    def __init__(self):
        self.script_path = "a.py"  # 要运行的脚本路径
        self.running_process = None

        # 定义每天的运行时间点（只需要时分秒，日期会自动更新为当天）
        self.schedule_times = [
            "09:00:00",
            "09:15:00",
            "09:30:00",
            "09:45:00",
            "10:00:00",
            "10:30:00",
            "10:45:00",
            "11:00:00",
            "11:15:00",
            "13:30:00",
            "13:45:00",
            "14:00:00",
            "14:15:00",
            "14:30:00",
            "14:45:00",
            "21:00:00",
            "21:15:00",
            "21:30:00",
            "21:45:00",
            "22:00:00",
            "22:15:00",
            "22:30:00",
            "22:45:00",
        ]

    def run_script(self):
        """
        运行a.py脚本
        """
        try:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logging.info(f"开始执行任务 - {current_time}")

            # 检查脚本文件是否存在
            if not os.path.exists(self.script_path):
                logging.error(f"脚本文件 {self.script_path} 不存在")
                return

            # 如果有正在运行的进程，先终止它
            if self.running_process and self.running_process.poll() is None:
                logging.warning("检测到之前的任务仍在运行，正在终止...")
                self.running_process.terminate()
                time.sleep(5)  # 等待5秒让进程正常终止
                if self.running_process.poll() is None:
                    self.running_process.kill()  # 强制终止
                    logging.warning("强制终止了之前的任务")

            # 启动新的进程
            logging.info(f"正在启动脚本: {self.script_path}")
            self.running_process = subprocess.Popen(
                [sys.executable, self.script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )

            logging.info(f"任务已启动，进程ID: {self.running_process.pid}")

        except Exception as e:
            logging.error(f"执行任务时发生错误: {e}")

    def setup_schedule(self):
        """
        设置定时任务
        """
        logging.info("正在设置定时任务...")

        for time_str in self.schedule_times:
            schedule.every().day.at(time_str).do(self.run_script)
            logging.info(f"已设置定时任务: 每天 {time_str}")

        logging.info(f"总共设置了 {len(self.schedule_times)} 个定时任务")

    def get_next_run_time(self):
        """
        获取下一次运行时间
        """
        next_job = schedule.next_run()
        if next_job:
            return next_job.strftime('%Y-%m-%d %H:%M:%S')
        return "无"

    def show_schedule_info(self):
        """
        显示调度信息
        """
        logging.info("=== 定时任务调度信息 ===")
        logging.info(f"脚本路径: {self.script_path}")
        logging.info(f"任务数量: {len(self.schedule_times)}")
        logging.info(f"下次运行时间: {self.get_next_run_time()}")
        logging.info("每日运行时间点:")
        for i, time_str in enumerate(self.schedule_times, 1):
            logging.info(f"  {i:2d}. {time_str}")
        logging.info("========================")

    def run_scheduler(self):
        """
        运行调度器主循环
        """
        try:
            self.setup_schedule()
            self.show_schedule_info()

            logging.info("定时任务调度器已启动，按 Ctrl+C 停止")

            while True:
                schedule.run_pending()
                time.sleep(1)  # 每秒检查一次

        except KeyboardInterrupt:
            logging.info("收到停止信号，正在关闭调度器...")

            # 终止正在运行的进程
            if self.running_process and self.running_process.poll() is None:
                logging.info("正在终止运行中的任务...")
                self.running_process.terminate()
                time.sleep(5)
                if self.running_process.poll() is None:
                    self.running_process.kill()
                    logging.info("强制终止了运行中的任务")

            logging.info("调度器已停止")

        except Exception as e:
            logging.error(f"调度器运行时发生错误: {e}")

    def test_run(self):
        """
        手动测试运行脚本
        """
        logging.info("手动测试运行脚本...")
        self.run_script()

    def check_script_status(self):
        """
        检查脚本运行状态
        """
        if self.running_process is None:
            return "未运行"
        elif self.running_process.poll() is None:
            return f"运行中 (PID: {self.running_process.pid})"
        else:
            return f"已结束 (退出码: {self.running_process.returncode})"

    def show_status(self):
        """
        显示当前状态
        """
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        logging.info("=== 当前状态 ===")
        logging.info(f"当前时间: {current_time}")
        logging.info(f"脚本状态: {self.check_script_status()}")
        logging.info(f"下次运行: {self.get_next_run_time()}")
        logging.info("================")

def main():
    """
    主函数
    """
    print("=" * 50)
    print("定时任务调度器")
    print("=" * 50)

    # 检查是否需要安装schedule库
    try:
        import schedule
    except ImportError:
        print("错误: 缺少 schedule 库")
        print("请运行: pip install schedule")
        return

    scheduler = TaskScheduler()

    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "test":
            print("执行测试运行...")
            scheduler.test_run()
            return
        elif command == "status":
            print("显示状态信息...")
            scheduler.show_status()
            return
        elif command == "info":
            print("显示调度信息...")
            scheduler.setup_schedule()
            scheduler.show_schedule_info()
            return
        else:
            print("可用命令:")
            print("  python b.py        - 启动定时调度器")
            print("  python b.py test   - 手动测试运行脚本")
            print("  python b.py status - 显示当前状态")
            print("  python b.py info   - 显示调度信息")
            return

    # 启动调度器
    scheduler.run_scheduler()

if __name__ == "__main__":
    main()